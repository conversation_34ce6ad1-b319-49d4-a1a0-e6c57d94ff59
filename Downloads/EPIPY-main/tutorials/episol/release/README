-------------------------------------------------------------------------------

                            A brief user guide
                             EPRISM3D v 1.2.1

-------------------------------------------------------------------------------

1. Installation

Before installation, please make the following files executable: configure, 
config_aux/*:
chmod -R +x configure config_aux

EPRISM3d uses the standard autoconf and automake for installations like most GNU
programs. Normally, EPRISM3D is installed like:

./configure --with-fftw=$FFTW_HOME_FOLDER
make
make install

If you already have FFTW installed in system folders, then you won't need the
--with-fftw option. FFTW_HOME_FOLDER is the root path of FFTW3, which should 
contain at least two files: $FFTW_HOME_FOLDER/include/fftw3.h,
$FFTW_HOME_FOLDER/include/libfftw3.* (normally it is libfftw3.a).

If you want to read XTC trajectories, then you need to install like this:

./configure --with-fftw=$FFTW_HOME_FOLDER --with-gmx=$GROMACS_HOME_FOLDER
make
make install

If you already have GROMACS installed in system folders, then you won't need
the --with-gmx option. GROMACS_HOME_FOLDER is the root folder of GROMACS. In
GROMACS 4, xtcio.h should be found in $GROMACS_HOME_FOLDER/include/gromacs, and
libgmx.a should be found in $GROMACS_HOME_FOLDER/lib. In GROMACS 5~2019,
xtcio.h should be found in $GROMACS_HOME_FOLDER/include/gromacs/fileio, and
libgromacs.a should be found in $GROMACS_HOME_FOLDER/lib.

Currently autoconf only support GROMACS 4 and 5.0. If you compile manually, you
can use GROMACS 5.1~2019. GROMACS 2020 and later can't be used since they don't
have the $GROMACS_HOME_FOLDER/include folder (unless you copy them from the
folder you build GROMACS).

EPRISM3D also uses pthread and ZLIB. These libraries are optional and detected
automatically.

If installed successfully, you should have the following files (the home folder
of EPRISM3D can be redirected by --prefix):

EPRISM3D_HOME_FOLDER
├── bin
│   ├── episol              A binary with rism3d/top2solute/gensolvent/ts4sdump
│   ├── eprism3d            The binary to perform 3DRISM and HI calculations
│   ├── gensolvent          Helps to generate a solvent file
│   ├── gmxtop2solute       Generate  a solute file from TOP file of GROMACS
│   ├── ts4sdump            Translate compressed outputs of eprism3d to text
│   ├── heatmap             Plot cut-view of 3D distributions
│   └── generate-idc.sh     Generate IDC corrected solute files
└── share
    └── eprism3d
        ├── solute              some examples of solulte files
        │   ├── methane.gro     Conformation of a methane, GROMACS format
        │   ├── methane.pdb     Conformation of a methane, PDB format
        │   ├── methane.prmtop  Forcefield file of a methane, AMBER format
        │   ├── methane.top     TOP file of methane, GROMACS format
        │   ├── methane.solute  Solute file generated from methane.top
        │   └── methane.xtc     Conformation of a methane, GROMACS format
        └── solvent
            ├── tip3p-amber14.01A.gaff      Solvent file of TIP3P
            ├── tip3p-amber14.025A.gaff     Solvent file of TIP3P
            ├── gvv.tip3p.1DDRISM.01A.txt   Water-water RDF (dr=0.01A)
            └── gvv.tip3p.1DDRISM.025A.txt  Water-water RDF (dr=0.025A)

After installation, please add the paths of EPRISM3D to environment variables:

export PATH=$PATH:$EPRISM3D_HOME_FOLDER/bin
export IETLIB=$EPRISM3D_HOME_FOLDER/share/eprism3d/solvent

eprism3d will search solvent files in $IETLIB if it cannot find it from the
current working directory.

-------------------------------------------------------------------------------

2. A quick user guide

EPRISM3D needs three input files: the solute file, the solute conformation, and
the solvent file.


2.1. Generate a solute file

If you have the PRMTOP file, then you don't need this step.

The solute file can be generated from the TOP file of GROMACS. First, the
$GMXDATA variable should be correctly set to $GROMACS_HOME/share, which can be
simply set via source $GROMACS_HOME/bin/GMXRC. Then simply use the following
command to do the convertion:

gmxtop2solute -top methane.top [-abbreviate] -excl SOL -o methane.solute

Here -excl SOL will exclude all water molecules; -abbreviate is an optional
feature to generate smaller solute file by folding the duplication of atoms.
-o specifies the output file. If you don't specify -o then everything will be
printed to terminal.

If you want to do Ion-Dipole-Correction (IDC), then you can simply generate an
IDC corrected solute file:

bash generate-idc.sh methane.solute [serial] > methane-idc.solute

The solute_file.solute is the file generated by gmxtop2solute, and [serial] is
an option specifying dielectric constant mixing rule. Three rules can be used:
parallel, serial, serial2.


2.2. Generate a solvent file

The solvent file needs to be generated manually with the help of gmxtop2solute
and gensolvent. First, you need to generate a solute file from the TOP file
of the pure solvent system:

episol top2solute -solvent-format -top tip3p.top -ab |grep -v \# >tip3p.raw.txt

Then edit tip3p.raw.txt manually, change the 4th column of the last line to 2
so the HW2 and HW1 hydrogens are grouped to one site. Then:

episol gensolvent -p tip3p.raw.txt -s ../npt/tip3p-npt.gro -f \
../npt/tip3p-npt.xtc -ff gaff > tip3p.gaff

Finally, you need to manually add the following line of solvent-solvent
correlation to the [solvent] section of tip3p.gaff:

gvv 0.001 gvv.tip3p.1DDRISM.01A.txt


2.3. Perform EPRISM3D calculation

(1) The EPRISM3D is typically performed as:

episol rism3d -p solvent/tip3p-amber14.01A.gaff -s solute/methane.solute -f \
    solute/methane.pdb -nr 60x60x60 -rc 1 -do closure=hnc rism,step=1000 \
    report:mass,LJ,Coul,V,excess,excessGF

(2) If you want to generate the 3D distributions of density, total correlations
and direct correlations, then:

episol rism3d -p solvent/tip3p-amber14.01A.gaff -s solute/methane.solute -f \
    solute/methane.pdb -nr 60x60x60 -rc 1 -do closure=hnc rism,step=1000 \
    save:guv,huv,cuv -o distribution

Then distribution.ts4s will be generated. Then:

episol ts4sdump distribution.ts4s

The above command shows the file contains three frames. These frames can be
extracted with the following command::

episol ts4sdump -e 1 distribution.ts4s
episol ts4sdump -e 2 distribution.ts4s
episol ts4sdump -e 3 distribution.ts4s

Then you can generate a cut-view image of the 3D distribution:

printf "0 10 0 160\n1 255 255 255\n4 255 0 0\n" > color_map.txt
episol ts4sdump -e 1 distribution.ts4s | awk '$3==30{print $0}' > \
distribution.z_30.txt
heatmap -color color_map.txt -f distribution.z_30.txt -col 4 -nr 60x60 \
    -size 600x600 -o distribution.z_30.bmp
convert distribution.z_30.bmp distribution.z_30.png

(3) Compute RDFs:

episol rism3d -p solvent/tip3p-amber14.01A.gaff -s solute/methane.solute -f \
    solute/methane.pdb -nr 60x60x60 -rc 1 -do closure=hnc rism,step=1000 \
    report@end:rdf save@end:rdf -o methane-water -rdf-bins 50 -rdf-grps \
    1-1,2-1=2,3-tip3p:OW=2,MOL:H3-1=2,5-1=2,1-2

"report:rdf" will print the RDFs to screen, and "save:rdf" will save it to
a text file (e.g. here the RDF will be saved to methane-water.rdf). For a
trajectory containing multiple frames, "save:rdf" and "print:rdf" will display
the RDFs of each frame, while "save@end" and "print@end" will display the RDFs
of all frames. The -rdf-grps defines the pairs to compute. In the above
example, the first column of RDF corresponds to MET:C1-SOL:OW, the second
column corresponds to the average of MET:H1-SOL:OW ... MET:H4-SOL:OW, the
third column corresponds to MET:C1-SOL:HW. 

(4) Some tricks:

a. Change the precision of output data

eprism3d -%f ...        # output in float precision
eprism3d -%.15g ...     # output in double precision
eprism3d -%21.15e ...   # output in double precision

b. Change the convergence threshould

eprism3d -errtol 1e-12  # default setting
eprism3d -errtol 1e-7   # converge to 10^-7
eprism3d -errtol 1e-7 -allow-original-scf-error
                        # converge to 10^-7, error not scaled by -delvv

c. The convergence control:

episol rism3d -delvv 1 -ndiis 5 -dynamic-delvv 1 ...
episol rism3d -delvv 0.7 -ndiis 5 -dynamic-delvv 0 ...

d. Specify number of threads and nice level:

episol rism3d -nice 19 ...   # change the nice level
episol rism3d -nt 8 ...      # use 8 threads
episol rism3d -np 8 ...      # use 8 fork processes

e. The IDC is simply performed with the IDC-corrected solute file:

episol rism3d -p solvent/tip3p-amber14.01A.gaff -s methane-idc.solute ...

f. Perform 3DRISM-HI

First make sure the solvent file contains a [zeta] section. Then:

eprism3d -p solvent/tip3p-amber14.01A.gaff -s solute/methane.solute -f \
    solute/methane.pdb -nr 60x60x60 -rc 1 -do hi closure=hnc rism,step=1000

g. The cavity detection and removal can be enabled by -cr option:

episol rism3d ... -cr ...

h. Perform IET calculations for multiple solutes

Suppose you have a lot of solutes; the solute files (can be solute or prmtop
formats) and conformations are stored in solutes/ and pdbs/ folders,
respectively. Then eprism3d will automatically compute for all solute
molecules:

episol rism3d -p solvent/tip3p-amber14.01A.gaff -s solutes -f pdbs -ntb 20 -nt 2 ...

Here each solute will be computed with 2 threads (-nt 2), and 20 solutes
(-ntb 20) will be computed simultaneously. This will use totally 40 cores.

i. Do 3DRISM for a super large system

The EPRISM3D will detect the total amount of physical memory to prevent
exceeding of physical memory. In most cases, computer OS allows to exceed the
physical memory by using swap memories on hard drives. If you want to use more
than your physical memories, then you can turn off the memory detection of
eprism3d:

episol rism3d -allow-exceed-ram ...


