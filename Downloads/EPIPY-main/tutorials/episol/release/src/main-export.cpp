

#ifdef _FUNCTION_EXPORT_
    void build_solvent_charge_mesh(IET_Param * sys, IET_arrays * arr, __REAL__ *** charge_mesh, double shift_x, double shift_y, double shift_z){
        __REAL__ *** charge_grid = arr->fftout;
        size_t N3 = arr->nx * arr->ny * arr->nz; double dV = arr->box.x * arr->box.y * arr->box.z / N3;
      // 1. original charge distribution -> charge_grid[]
        clear_tensor3d(charge_grid, N3);
        for (int iv=0; iv<sys->nv; iv++){
            double charge = sys->av[iv].charge; double dN = dV * sys->density_av[iv];
            __REAL__ * huv1 = &arr->huv[iv][0][0][0]; __REAL__ * dd1 = arr->dd? &arr->dd[sys->av[iv].iaa][0][0][0] : nullptr; double nbulk = sys->nbulk[sys->av[iv].iaa];
            for (size_t i3=0; i3<N3; i3++){
                double g = (1+huv1[i3]) * (dd1? dd1[i3]/nbulk : 1);
                double dn = g * dN * sys->av[iv].multi;
                charge_grid[0][0][i3] += dn * charge;
            }
        }
      // 2. interporlate to grids with shift
        int nx = sys->nr[0]; int ny = sys->nr[1]; int nz = sys->nr[2];
        Vector box = sys->traj.box; double drx = box.x / sys->nr[0]; double dry = box.y / sys->nr[1]; double drz = box.z / sys->nr[2];
        clear_tensor3d(charge_mesh, N3);
        for (int gz=0; gz<arr->nz; gz++) for (int gy=0; gy<arr->ny; gy++) for (int gx=0; gx<arr->nx; gx++){
            if (sys->pbc_x) gx = ff_pbc_i(gx, nx); if (sys->pbc_y) gy = ff_pbc_i(gy, ny); if (sys->pbc_z) gz = ff_pbc_i(gz, nz);
            if (gx>=0 && gx<nx && gy>=0 && gy<ny && gz>=0 && gz<nz){
                int ig1[3], ig2[3]; double w1[3], w2[3]; bool in_box = true;
                ig1[0] = gx; ig2[0] = ig1[0] + 1; w2[0] = shift_x; w1[0] = 1 - shift_x;
                    ig1[0] = ff_pbc_i(ig1[0], nx); ig2[0] = ff_pbc_i(ig2[0], nx);
                ig1[1] = gy; ig2[1] = ig1[1] + 1; w2[1] = shift_y; w1[1] = 1 - shift_y;
                    ig1[1] = ff_pbc_i(ig1[1], ny); ig2[1] = ff_pbc_i(ig2[1], ny);
                ig1[2] = gz; ig2[2] = ig1[2] + 1; w2[2] = shift_z; w1[2] = 1 - shift_z;
                    ig1[2] = ff_pbc_i(ig1[2], nz); ig2[2] = ff_pbc_i(ig2[2], nz);
                if (in_box){
                    charge_mesh[ig1[2]][ig1[1]][ig1[0]] += charge_grid[gz][gy][gx] * w1[2]*w1[1]*w1[0];
                    charge_mesh[ig1[2]][ig1[1]][ig2[0]] += charge_grid[gz][gy][gx] * w1[2]*w1[1]*w2[0];
                    charge_mesh[ig1[2]][ig2[1]][ig1[0]] += charge_grid[gz][gy][gx] * w1[2]*w2[1]*w1[0];
                    charge_mesh[ig1[2]][ig2[1]][ig2[0]] += charge_grid[gz][gy][gx] * w1[2]*w2[1]*w2[0];
                    charge_mesh[ig2[2]][ig1[1]][ig1[0]] += charge_grid[gz][gy][gx] * w2[2]*w1[1]*w1[0];
                    charge_mesh[ig2[2]][ig1[1]][ig2[0]] += charge_grid[gz][gy][gx] * w2[2]*w1[1]*w2[0];
                    charge_mesh[ig2[2]][ig2[1]][ig1[0]] += charge_grid[gz][gy][gx] * w2[2]*w2[1]*w1[0];
                    charge_mesh[ig2[2]][ig2[1]][ig2[0]] += charge_grid[gz][gy][gx] * w2[2]*w2[1]*w2[0];
                } else charge_mesh[gz][gy][gx] += charge_grid[gz][gy][gx];
    //printf(":::: add %12f charge to %d %d %d: %.2f %.2f %.2f\n", sys->as[ja].charge, gx, gy, gz, vr.x/box.x*nx, vr.y/box.y*ny, vr.z/box.z*nz);
            }
        }

    }
    void calculate_solvent_ef(IET_Param * sys, IET_arrays * arr, double gamma){
        if (!sys || !arr) return ;
        int nx = sys->nr[0]; int ny = sys->nr[1]; int nz = sys->nr[2];
        __REAL__ *** phi= arr->rismhi_cache[0][0];
        __REAL__ *** Ex = arr->rismhi_cache[1][0];
        __REAL__ *** Ey = arr->rismhi_cache[2][0];
        __REAL__ *** Ez = arr->rismhi_cache[3][0];
      // calculate the electrostatic field generated by
        double shift_x = 0.001;
        double shift_y = 0.001;
        double shift_z = 0.001;
      // 1. mesh chages; 2. long range potential calculation: fft
        build_solvent_charge_mesh(sys, arr, arr->fftin, 0, 0, 0);

/*
FILE * ftemp1 = fopen("__solvent_charge_distribution.txt", "w");
if (ftemp1){
    for (int iz=0; iz<arr->nz; iz++) for (int iy=0; iy<arr->ny; iy++) for (int ix=0; ix<arr->nx; ix++){
        fprintf(ftemp1, "%3d %3d %3d %12.6g\n", ix, iy, iz, arr->fftin[iz][iy][ix]);
    }
    fclose(ftemp1);
}
*/

        perform_PME(sys, arr, gamma);
      // 3. Final step of short-range-screened-Coulomb potential
        for (int iz=0; iz<nz; iz++) for (int iy=0; iy<ny; iy++) for (int ix=0; ix<nx; ix++) phi[iz][iy][ix] = arr->fftin[iz][iy][ix];

      // 4. do partial derivatives and get long range Ecoul0
        build_solvent_charge_mesh(sys, arr, arr->fftin, shift_x, 0, 0); perform_PME(sys, arr, gamma);
        for (int iz=0; iz<nz; iz++) for (int iy=0; iy<ny; iy++) for (int ix=0; ix<nx; ix++) Ex[iz][iy][ix] = (arr->fftin[iz][iy][ix] - phi[iz][iy][ix]) / shift_x;
        build_solvent_charge_mesh(sys, arr, arr->fftin, 0, shift_y, 0); perform_PME(sys, arr, gamma);
        for (int iz=0; iz<nz; iz++) for (int iy=0; iy<ny; iy++) for (int ix=0; ix<nx; ix++) Ey[iz][iy][ix] = (arr->fftin[iz][iy][ix] - phi[iz][iy][ix]) / shift_y;
        build_solvent_charge_mesh(sys, arr, arr->fftin, 0, 0, shift_z); perform_PME(sys, arr, gamma);
        for (int iz=0; iz<nz; iz++) for (int iy=0; iy<ny; iy++) for (int ix=0; ix<nx; ix++) Ez[iz][iy][ix] = (arr->fftin[iz][iy][ix] - phi[iz][iy][ix]) / shift_z;

/*
FILE * ftemp = fopen("__calculate_solvent_ef_temp.txt", "w");
if (ftemp){
    for (int iz=0; iz<arr->nz; iz++) for (int iy=0; iy<arr->ny; iy++) for (int ix=0; ix<arr->nx; ix++){
        fprintf(ftemp, "%3d %3d %3d %12.6g %12.6g %12.6g %12.6g\n", ix, iy, iz, arr->rismhi_cache[2][0][iz][iy][ix], arr->rismhi_cache[3][0][iz][iy][ix], arr->rismhi_cache[4][0][iz][iy][ix], arr->rismhi_cache[5][0][iz][iy][ix]);
    }
    fclose(ftemp);
}
*/
    }

    /*
    // ====== debug process ======
    // debug code in main() to perform solvent electrostatic field calculation
        double gamma = Vector(arr->nx, arr->ny, arr->nz).mod()/arr->box.mod()*100;
        calculate_solvent_ef(sys, arr, gamma);

        FILE * fout = nullptr;
        append_save_data_immediately(sys, arr, global_flog, &arr->huv[0][0][0][0], arr->nx, arr->ny, arr->nz, arr->nv, &fout, "test.ts4s", "huv");
        for (size_t i3=0; i3<N3; i3++) arr->res[0][0][0][i3] = arr->ucoulsr[0][0][i3] + arr->ucoullr[0][0][i3];
        append_save_data_immediately(sys, arr, global_flog, &arr->res[0][0][0][0], arr->nx, arr->ny, arr->nz, 1, &fout, "test.ts4s", "coul");
        append_save_data_immediately(sys, arr, global_flog, &arr->Ecoul0[0][0][0][0], arr->nx, arr->ny, arr->nz, 3, &fout, "test.ts4s", "Ef");

        append_save_data_immediately(sys, arr, global_flog, &arr->rismhi_cache[2][0][0][0][0], arr->nx, arr->ny, arr->nz, 1, &fout, "test.ts4s", "Efv", "solvent:coul");
        append_save_data_immediately(sys, arr, global_flog, &arr->rismhi_cache[3][0][0][0][0], arr->nx, arr->ny, arr->nz, 1, &fout, "test.ts4s", "Efv", "solvent:Ex");
        append_save_data_immediately(sys, arr, global_flog, &arr->rismhi_cache[4][0][0][0][0], arr->nx, arr->ny, arr->nz, 1, &fout, "test.ts4s", "Efv", "solvent:Ey");
        append_save_data_immediately(sys, arr, global_flog, &arr->rismhi_cache[5][0][0][0][0], arr->nx, arr->ny, arr->nz, 1, &fout, "test.ts4s", "Efv", "solvent:Ez");
        if (fout) fclose(fout);

    // debug code for C/FORTRAN interface
        extern "C" int return_c_int3_(int & input, int & output){
            output = input;
            return output;
        }
        // corresponding FORTRAN code:
        //    ...
        //    implicit none
        //    integer :: return_c_int3
        //    ...
        //    integer,dimension(1),target:: success
        //    integer:: i
        //    ...
        //    success = 0
        //    write(*,*) 'original with ', success
        //    i = return_c_int3(123, success)
        //    write(*,*) 'C return with ', success(1), i

    // ==== debug process end ====
    */
#endif

#ifdef _FUNCTION_EXPORT_
    extern "C" void calculate_default_solvent_ef(double & gamma){
        calculate_solvent_ef(global_sys, global_arr, gamma);
    }
    extern "C" double * get_default_lj(){ return global_arr?(global_arr->ulj?&global_arr->ulj[0][0][0][0]:nullptr):nullptr; }
    extern "C" double * get_default_solute_Ef_phi(){ return global_arr?(global_arr->ucoulsr?&global_arr->ucoulsr[0][0][0]:nullptr):nullptr; }
    extern "C" double * get_default_solute_Ef_x(){ return global_arr?(global_arr->Ecoul0?&global_arr->Ecoul0[0][0][0][0]:nullptr):nullptr; }
    extern "C" double * get_default_solute_Ef_y(){ return global_arr?(global_arr->Ecoul0?&global_arr->Ecoul0[1][0][0][0]:nullptr):nullptr; }
    extern "C" double * get_default_solute_Ef_z(){ return global_arr?(global_arr->Ecoul0?&global_arr->Ecoul0[2][0][0][0]:nullptr):nullptr; }
    extern "C" void get_default_solute_Ef(__REAL__ ** phi, __REAL__ ** Ex, __REAL__ ** Ey, __REAL__ ** Ez){
        *phi = get_default_solute_Ef_phi();
        *Ex = get_default_solute_Ef_x();
        *Ey = get_default_solute_Ef_y();
        *Ez = get_default_solute_Ef_z();
    }
    extern "C" double * get_default_solvent_Ef_phi(){
        return global_arr?(global_arr->rismhi_cache[2]?&global_arr->rismhi_cache[2][0][0][0][0]:nullptr):nullptr;
    }
    extern "C" double * get_default_solvent_Ef_x(){
        return global_arr?(global_arr->rismhi_cache[3]?&global_arr->rismhi_cache[3][0][0][0][0]:nullptr):nullptr;
    }
    extern "C" double * get_default_solvent_Ef_y(){
        return global_arr?(global_arr->rismhi_cache[4]?&global_arr->rismhi_cache[4][0][0][0][0]:nullptr):nullptr;
    }
    extern "C" double * get_default_solvent_Ef_z(){
        return global_arr?(global_arr->rismhi_cache[5]?&global_arr->rismhi_cache[5][0][0][0][0]:nullptr):nullptr;
    }
    extern "C" void get_default_solvent_Ef(__REAL__ ** phi, __REAL__ ** Ex, __REAL__ ** Ey, __REAL__ ** Ez){
        *phi = get_default_solvent_Ef_phi();
        *Ex = get_default_solvent_Ef_x();
        *Ey = get_default_solvent_Ef_y();
        *Ez = get_default_solvent_Ef_z();
    }
#endif

#ifdef _FUNCTION_EXPORT_
    extern "C" bool rismhi3d_initialization(char * & argline){
        StringNS::string args[1000]; char * argv[1000];
        int argc = StringNS::analysis_line(argline, args, sizeof(argv)/sizeof(argv[0]), true);
        for (int i=0; i<1000&&i<argc; i++) argv[i] = args[i].text;

        int error = false; bool syntax_error = false;
        return main_initialization(argc, argv, &global_sys, &global_arr, &global_flog, &global_rdf_datas, &error, &syntax_error);
    }
    extern "C" bool rismhi3d_initialization_(char * & argline){
        return rismhi3d_initialization(argline);
    }

    extern "C" void rismhi3d_dispose(){
        main_dispose(global_sys, global_arr, &global_flog, true);
    }
    extern "C" void rismhi3d_dispose_(){ rismhi3d_dispose(); }
    extern "C" void rismhi3d_dispose_on_failure(){
        main_dispose(global_sys, global_arr, &global_flog, false);
    }
    extern "C" void rismhi3d_dispose_on_failure_(){ rismhi3d_dispose_on_failure(); }
    extern "C" int rismhi3d_run_commands(int & nframe, double & time){
        TPAppendix tpa; tpa.time = time; tpa.prec = 0; tpa.step = nframe;
        RDF_data * rdf = global_rdf_datas.rdf_datas[0];
        RDF_data * rdfs = global_rdf_datas.rdf_datas[1];
        return main_run_commands(global_sys, global_arr, global_flog, rdf, rdfs, nframe, &tpa);
    }
    extern "C" int rismhi3d_run_commands_(int & nframe, double & time){
        return rismhi3d_run_commands(nframe, time);
    }
    extern "C" bool rismhi3d_run_finalizing_commands(int & nframe, double & time){
        TPAppendix tpa; tpa.time = time; tpa.prec = 0; tpa.step = nframe;
        RDF_data * rdf = global_rdf_datas.rdf_datas[0];
        RDF_data * rdfs = global_rdf_datas.rdf_datas[1];
        return main_run_finalizing_commands(global_sys, global_arr, global_flog, rdf, rdfs, &file_out, nframe, tpa);
    }
    extern "C" bool rismhi3d_run_finalizing_commands_(int & nframe, double & time){
        return rismhi3d_run_finalizing_commands(nframe, time);
    }
    extern "C" int rismhi3d_read_frame(int & frame, double & time){
        TPAppendix tpa; tpa.time = time; tpa.prec = 0; tpa.step = frame;
        return read_frame(&global_sys->traj, &frame, &tpa);
    }
    extern "C" int rismhi3d_read_frame_(int & frame, double & time){
        return rismhi3d_read_frame(frame, time);
    }

#endif
