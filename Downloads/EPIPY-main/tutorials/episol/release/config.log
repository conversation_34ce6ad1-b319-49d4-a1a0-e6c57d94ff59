This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by eprism3d configure 1.2.6, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  $ ./configure --with-fftw=/Users/<USER>/episol/fftw-3.3.8

## --------- ##
## Platform. ##
## --------- ##

hostname = chonoMacBook-Air.local
uname -m = arm64
uname -r = 24.1.0
uname -s = Darwin
uname -v = Darwin Kernel Version 24.1.0: Thu Oct 10 21:02:45 PDT 2024; root:xnu-11215.41.3~2/RELEASE_ARM64_T8112

/usr/bin/uname -p = arm
/bin/uname -X     = unknown

/bin/arch              = unknown
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = Mach kernel version:
	 Darwin Kernel Version 24.1.0: Thu Oct 10 21:02:45 PDT 2024; root:xnu-11215.41.3~2/RELEASE_ARM64_T8112
Kernel configured for up to 8 processors.
8 processors are physically available.
8 processors are logically available.
Processor type: arm64e (ARM64E)
Processors active: 0 1 2 3 4 5 6 7
Primary memory available: 24.00 gigabytes
Default processor set: 655 tasks, 6505 threads, 8 processors
Load average: 12.99, Mach factor: 0.91
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /Users/<USER>/anaconda3/envs/zept/bin/
PATH: /Users/<USER>/google-cloud-sdk/bin/
PATH: /Users/<USER>/anaconda3/envs/zept/bin/
PATH: /Users/<USER>/anaconda3/condabin/
PATH: /opt/homebrew/bin/
PATH: /opt/homebrew/sbin/
PATH: /usr/local/bin/
PATH: /System/Cryptexes/App/usr/bin/
PATH: /usr/bin/
PATH: /bin/
PATH: /usr/sbin/
PATH: /sbin/
PATH: /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/
PATH: /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/
PATH: /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/
PATH: /Users/<USER>/.orbstack/bin/


## ----------- ##
## Core tests. ##
## ----------- ##

configure:2444: looking for aux files: compile missing install-sh
configure:2457:  trying ./config_aux/
configure:2486:   ./config_aux/compile found
configure:2486:   ./config_aux/missing found
configure:2468:   ./config_aux/install-sh found
configure:2616: checking for a BSD-compatible install
configure:2689: result: /usr/bin/install -c
configure:2700: checking whether build environment is sane
configure:2755: result: yes
configure:2914: checking for a race-free mkdir -p
configure:2958: result: ./config_aux/install-sh -c -d
configure:2965: checking for gawk
configure:3000: result: no
configure:2965: checking for mawk
configure:3000: result: no
configure:2965: checking for nawk
configure:3000: result: no
configure:2965: checking for awk
configure:2986: found /usr/bin/awk
configure:2997: result: awk
configure:3008: checking whether make sets $(MAKE)
configure:3031: result: yes
configure:3061: checking whether make supports nested variables
configure:3079: result: yes
configure:3279: checking for gcc
configure:3300: found /usr/bin/gcc
configure:3311: result: gcc
configure:3664: checking for C compiler version
configure:3673: gcc --version >&5
Apple clang version 16.0.0 (clang-1600.0.26.4)
Target: arm64-apple-darwin24.1.0
Thread model: posix
InstalledDir: /Library/Developer/CommandLineTools/usr/bin
configure:3684: $? = 0
configure:3673: gcc -v >&5
Apple clang version 16.0.0 (clang-1600.0.26.4)
Target: arm64-apple-darwin24.1.0
Thread model: posix
InstalledDir: /Library/Developer/CommandLineTools/usr/bin
configure:3684: $? = 0
configure:3673: gcc -V >&5
clang: error: argument to '-V' is missing (expected 1 value)
clang: error: no input files
configure:3684: $? = 1
configure:3673: gcc -qversion >&5
clang: error: unknown argument '-qversion'; did you mean '--version'?
clang: error: no input files
configure:3684: $? = 1
configure:3673: gcc -version >&5
clang: error: unknown argument '-version'; did you mean '--version'?
clang: error: no input files
configure:3684: $? = 1
configure:3704: checking whether the C compiler works
configure:3726: gcc    conftest.c  >&5
configure:3730: $? = 0
configure:3780: result: yes
configure:3783: checking for C compiler default output file name
configure:3785: result: a.out
configure:3791: checking for suffix of executables
configure:3798: gcc -o conftest    conftest.c  >&5
configure:3802: $? = 0
configure:3825: result: 
configure:3847: checking whether we are cross compiling
configure:3855: gcc -o conftest    conftest.c  >&5
configure:3859: $? = 0
configure:3866: ./conftest
configure:3870: $? = 0
configure:3885: result: no
configure:3890: checking for suffix of object files
configure:3913: gcc -c   conftest.c >&5
configure:3917: $? = 0
configure:3939: result: o
configure:3943: checking whether the compiler supports GNU C
configure:3963: gcc -c   conftest.c >&5
configure:3963: $? = 0
configure:3973: result: yes
configure:3984: checking whether gcc accepts -g
configure:4005: gcc -c -g  conftest.c >&5
configure:4005: $? = 0
configure:4049: result: yes
configure:4069: checking for gcc option to enable C11 features
configure:4084: gcc  -c -g -O2  conftest.c >&5
conftest.c:25:14: warning: a function definition without a prototype is deprecated in all versions of C and is not supported in C2x [-Wdeprecated-non-prototype]
   25 | static char *e (p, i)
      |              ^
1 warning generated.
configure:4084: $? = 0
configure:4102: result: none needed
configure:4218: checking whether gcc understands -c and -o together
configure:4241: gcc -c conftest.c -o conftest2.o
configure:4244: $? = 0
configure:4241: gcc -c conftest.c -o conftest2.o
configure:4244: $? = 0
configure:4256: result: yes
configure:4276: checking whether make supports the include directive
configure:4291: make -f confmf.GNU && cat confinc.out
this is the am__doit target
configure:4294: $? = 0
configure:4313: result: yes (GNU style)
configure:4339: checking dependency style of gcc
configure:4451: result: gcc3
configure:4535: checking for g++
configure:4556: found /usr/bin/g++
configure:4567: result: g++
configure:4594: checking for C++ compiler version
configure:4603: g++ --version >&5
Apple clang version 16.0.0 (clang-1600.0.26.4)
Target: arm64-apple-darwin24.1.0
Thread model: posix
InstalledDir: /Library/Developer/CommandLineTools/usr/bin
configure:4614: $? = 0
configure:4603: g++ -v >&5
Apple clang version 16.0.0 (clang-1600.0.26.4)
Target: arm64-apple-darwin24.1.0
Thread model: posix
InstalledDir: /Library/Developer/CommandLineTools/usr/bin
configure:4614: $? = 0
configure:4603: g++ -V >&5
clang++: error: argument to '-V' is missing (expected 1 value)
clang++: error: no input files
configure:4614: $? = 1
configure:4603: g++ -qversion >&5
clang++: error: unknown argument '-qversion'; did you mean '--version'?
clang++: error: no input files
configure:4614: $? = 1
configure:4618: checking whether the compiler supports GNU C++
configure:4638: g++ -c   conftest.cpp >&5
configure:4638: $? = 0
configure:4648: result: yes
configure:4659: checking whether g++ accepts -g
configure:4680: g++ -c -g  conftest.cpp >&5
configure:4680: $? = 0
configure:4724: result: yes
configure:4744: checking for g++ option to enable C++11 features
configure:4759: g++  -c -g -O2  conftest.cpp >&5
conftest.cpp:57:3: error: "Compiler does not advertise C++11 conformance"
   57 | # error "Compiler does not advertise C++11 conformance"
      |   ^
conftest.cpp:62:3: error: unknown type name 'constexpr'
   62 |   constexpr int get_val() { return 20; }
      |   ^
conftest.cpp:74:17: error: delegating constructors are permitted only in C++11
   74 |     delegate(): delegate(2354) {}
      |                 ^~~~~~~~
conftest.cpp:85:26: warning: 'override' keyword is a C++11 extension [-Wc++11-extensions]
   85 |     virtual int getval() override final { return this->n * 2; }
      |                          ^
conftest.cpp:85:35: warning: 'final' keyword is a C++11 extension [-Wc++11-extensions]
   85 |     virtual int getval() override final { return this->n * 2; }
      |                                   ^
conftest.cpp:92:16: warning: defaulted function definitions are a C++11 extension [-Wc++11-extensions]
   92 |     nocopy() = default;
      |                ^
conftest.cpp:93:29: warning: deleted function definitions are a C++11 extension [-Wc++11-extensions]
   93 |     nocopy(const nocopy&) = delete;
      |                             ^
conftest.cpp:94:41: warning: deleted function definitions are a C++11 extension [-Wc++11-extensions]
   94 |     nocopy & operator=(const nocopy&) = delete;
      |                                         ^
conftest.cpp:106:25: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  106 |   template <typename V> auto sum(V first) -> V
      |                         ^
conftest.cpp:106:25: error: 'auto' not allowed in function return type
  106 |   template <typename V> auto sum(V first) -> V
      |                         ^~~~
conftest.cpp:106:42: error: expected ';' at end of declaration
  106 |   template <typename V> auto sum(V first) -> V
      |                                          ^
      |                                          ;
conftest.cpp:106:43: error: cannot use arrow operator on a type
  106 |   template <typename V> auto sum(V first) -> V
      |                                           ^
conftest.cpp:110:33: warning: variadic templates are a C++11 extension [-Wc++11-extensions]
  110 |   template <typename V, typename... Args> auto sum(V first, Args... rest) -> V
      |                                 ^
conftest.cpp:110:43: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  110 |   template <typename V, typename... Args> auto sum(V first, Args... rest) -> V
      |                                           ^
conftest.cpp:110:43: error: 'auto' not allowed in function return type
  110 |   template <typename V, typename... Args> auto sum(V first, Args... rest) -> V
      |                                           ^~~~
conftest.cpp:110:74: error: expected ';' at end of declaration
  110 |   template <typename V, typename... Args> auto sum(V first, Args... rest) -> V
      |                                                                          ^
      |                                                                          ;
conftest.cpp:110:75: error: cannot use arrow operator on a type
  110 |   template <typename V, typename... Args> auto sum(V first, Args... rest) -> V
      |                                                                           ^
conftest.cpp:134:3: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  134 |   auto a1 = 6538;
      |   ^
conftest.cpp:135:3: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  135 |   auto a2 = 48573953.4;
      |   ^
conftest.cpp:136:3: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  136 |   auto a3 = "String literal";
      |   ^
conftest.cpp:139:8: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  139 |   for (auto i = a3; *i; ++i) { total += *i; }
      |        ^
conftest.cpp:155:8: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  155 |   for (auto &x : array) { x += 23; }
      |        ^
conftest.cpp:155:16: warning: range-based for loop is a C++11 extension [-Wc++11-extensions]
  155 |   for (auto &x : array) { x += 23; }
      |                ^
conftest.cpp:160:17: error: expected expression
  160 |   assert (eval ([](int x) { return x*2; }, 21) == 42);
      |                 ^
conftest.cpp:162:17: error: expected expression
  162 |   assert (eval ([&](double x) { return d += x; }, 3.0) == 5.0);
      |                 ^
conftest.cpp:164:17: error: expected expression
  164 |   assert (eval ([=](double x) mutable { return d += x; }, 4.0) == 9.0);
      |                 ^
conftest.cpp:170:3: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  170 |   auto a = sum(1);
      |   ^
conftest.cpp:170:12: error: no matching function for call to 'sum'
  170 |   auto a = sum(1);
      |            ^~~
conftest.cpp:171:3: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  171 |   auto b = sum(1, 2);
      |   ^
conftest.cpp:171:12: error: no matching function for call to 'sum'
  171 |   auto b = sum(1, 2);
      |            ^~~
conftest.cpp:172:3: warning: 'auto' type specifier is a C++11 extension [-Wc++11-extensions]
  172 |   auto c = sum(1.0, 2.0, 3.0);
      |   ^
conftest.cpp:172:12: error: no matching function for call to 'sum'
  172 |   auto c = sum(1.0, 2.0, 3.0);
      |            ^~~
conftest.cpp:177:25: warning: empty parentheses interpreted as a function declaration [-Wvexing-parse]
  177 |   cxx11test::delegate d2();
      |                         ^~
conftest.cpp:177:25: note: remove parentheses to declare a variable
  177 |   cxx11test::delegate d2();
      |                         ^~
conftest.cpp:190:16: error: found '<::' after a template name which forms the digraph '<:' (aka '[') and a ':', did you mean '< ::'?
  190 |   test_template<::test_template<int>> v(test_template<int>(12));
      |                ^~~
      |                < ::
conftest.cpp:190:36: error: a space is required between consecutive right angle brackets (use '> >')
  190 |   test_template<::test_template<int>> v(test_template<int>(12));
      |                                    ^~
      |                                    > >
conftest.cpp:194:22: error: use of undeclared identifier 'u8'
  194 |   char const *utf8 = u8"UTF-8 string \u2500";
      |                      ^
conftest.cpp:194:24: error: expected ';' at end of declaration
  194 |   char const *utf8 = u8"UTF-8 string \u2500";
      |                        ^
      |                        ;
fatal error: too many errors emitted, stopping now [-ferror-limit=]
18 warnings and 20 errors generated.
configure:4759: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "eprism3d"
| #define PACKAGE_TARNAME "eprism3d"
| #define PACKAGE_VERSION "1.2.6"
| #define PACKAGE_STRING "eprism3d 1.2.6"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "eprism3d"
| #define VERSION "1.2.6"
| /* end confdefs.h.  */
| 
| // Does the compiler advertise C++98 conformance?
| #if !defined __cplusplus || __cplusplus < 199711L
| # error "Compiler does not advertise C++98 conformance"
| #endif
| 
| // These inclusions are to reject old compilers that
| // lack the unsuffixed header files.
| #include <cstdlib>
| #include <exception>
| 
| // <cassert> and <cstring> are *not* freestanding headers in C++98.
| extern void assert (int);
| namespace std {
|   extern int strcmp (const char *, const char *);
| }
| 
| // Namespaces, exceptions, and templates were all added after "C++ 2.0".
| using std::exception;
| using std::strcmp;
| 
| namespace {
| 
| void test_exception_syntax()
| {
|   try {
|     throw "test";
|   } catch (const char *s) {
|     // Extra parentheses suppress a warning when building autoconf itself,
|     // due to lint rules shared with more typical C programs.
|     assert (!(strcmp) (s, "test"));
|   }
| }
| 
| template <typename T> struct test_template
| {
|   T const val;
|   explicit test_template(T t) : val(t) {}
|   template <typename U> T add(U u) { return static_cast<T>(u) + val; }
| };
| 
| } // anonymous namespace
| 
| 
| // Does the compiler advertise C++ 2011 conformance?
| #if !defined __cplusplus || __cplusplus < 201103L
| # error "Compiler does not advertise C++11 conformance"
| #endif
| 
| namespace cxx11test
| {
|   constexpr int get_val() { return 20; }
| 
|   struct testinit
|   {
|     int i;
|     double d;
|   };
| 
|   class delegate
|   {
|   public:
|     delegate(int n) : n(n) {}
|     delegate(): delegate(2354) {}
| 
|     virtual int getval() { return this->n; };
|   protected:
|     int n;
|   };
| 
|   class overridden : public delegate
|   {
|   public:
|     overridden(int n): delegate(n) {}
|     virtual int getval() override final { return this->n * 2; }
|   };
| 
|   class nocopy
|   {
|   public:
|     nocopy(int i): i(i) {}
|     nocopy() = default;
|     nocopy(const nocopy&) = delete;
|     nocopy & operator=(const nocopy&) = delete;
|   private:
|     int i;
|   };
| 
|   // for testing lambda expressions
|   template <typename Ret, typename Fn> Ret eval(Fn f, Ret v)
|   {
|     return f(v);
|   }
| 
|   // for testing variadic templates and trailing return types
|   template <typename V> auto sum(V first) -> V
|   {
|     return first;
|   }
|   template <typename V, typename... Args> auto sum(V first, Args... rest) -> V
|   {
|     return first + sum(rest...);
|   }
| }
| 
| 
| int
| main (int argc, char **argv)
| {
|   int ok = 0;
|   
|   assert (argc);
|   assert (! argv[0]);
| {
|   test_exception_syntax ();
|   test_template<double> tt (2.0);
|   assert (tt.add (4) == 6.0);
|   assert (true && !false);
| }
| 
|   
| {
|   // Test auto and decltype
|   auto a1 = 6538;
|   auto a2 = 48573953.4;
|   auto a3 = "String literal";
| 
|   int total = 0;
|   for (auto i = a3; *i; ++i) { total += *i; }
| 
|   decltype(a2) a4 = 34895.034;
| }
| {
|   // Test constexpr
|   short sa[cxx11test::get_val()] = { 0 };
| }
| {
|   // Test initializer lists
|   cxx11test::testinit il = { 4323, 435234.23544 };
| }
| {
|   // Test range-based for
|   int array[] = {9, 7, 13, 15, 4, 18, 12, 10, 5, 3,
|                  14, 19, 17, 8, 6, 20, 16, 2, 11, 1};
|   for (auto &x : array) { x += 23; }
| }
| {
|   // Test lambda expressions
|   using cxx11test::eval;
|   assert (eval ([](int x) { return x*2; }, 21) == 42);
|   double d = 2.0;
|   assert (eval ([&](double x) { return d += x; }, 3.0) == 5.0);
|   assert (d == 5.0);
|   assert (eval ([=](double x) mutable { return d += x; }, 4.0) == 9.0);
|   assert (d == 5.0);
| }
| {
|   // Test use of variadic templates
|   using cxx11test::sum;
|   auto a = sum(1);
|   auto b = sum(1, 2);
|   auto c = sum(1.0, 2.0, 3.0);
| }
| {
|   // Test constructor delegation
|   cxx11test::delegate d1;
|   cxx11test::delegate d2();
|   cxx11test::delegate d3(45);
| }
| {
|   // Test override and final
|   cxx11test::overridden o1(55464);
| }
| {
|   // Test nullptr
|   char *c = nullptr;
| }
| {
|   // Test template brackets
|   test_template<::test_template<int>> v(test_template<int>(12));
| }
| {
|   // Unicode literals
|   char const *utf8 = u8"UTF-8 string \u2500";
|   char16_t const *utf16 = u"UTF-8 string \u2500";
|   char32_t const *utf32 = U"UTF-32 string \u2500";
| }
| 
|   return ok;
| }
| 
configure:4759: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
conftest.cpp:177:25: warning: empty parentheses interpreted as a function declaration [-Wvexing-parse]
  177 |   cxx11test::delegate d2();
      |                         ^~
conftest.cpp:177:25: note: remove parentheses to declare a variable
  177 |   cxx11test::delegate d2();
      |                         ^~
1 warning generated.
configure:4759: $? = 0
configure:4780: result: -std=gnu++11
configure:4843: checking dependency style of g++ -std=gnu++11
configure:4955: result: gcc3
configure:5003: checking for stdio.h
configure:5003: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5003: $? = 0
configure:5003: result: yes
configure:5003: checking for stdlib.h
configure:5003: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5003: $? = 0
configure:5003: result: yes
configure:5003: checking for string.h
configure:5003: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5003: $? = 0
configure:5003: result: yes
configure:5003: checking for inttypes.h
configure:5003: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5003: $? = 0
configure:5003: result: yes
configure:5003: checking for stdint.h
configure:5003: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5003: $? = 0
configure:5003: result: yes
configure:5003: checking for strings.h
configure:5003: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5003: $? = 0
configure:5003: result: yes
configure:5003: checking for sys/stat.h
configure:5003: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5003: $? = 0
configure:5003: result: yes
configure:5003: checking for sys/types.h
configure:5003: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5003: $? = 0
configure:5003: result: yes
configure:5003: checking for unistd.h
configure:5003: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5003: $? = 0
configure:5003: result: yes
configure:5030: checking for pthread.h
configure:5030: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5030: $? = 0
configure:5030: result: yes
configure:5042: checking for zlib.h
configure:5042: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5042: $? = 0
configure:5042: result: yes
configure:5074: checking for /Users/<USER>/episol/fftw-3.3.8/include/fftw3.h
configure:5074: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
configure:5074: $? = 0
configure:5074: result: yes
configure:5094: checking for gromacs/xtcio.h
configure:5094: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
conftest.cpp:52:10: fatal error: 'gromacs/xtcio.h' file not found
   52 | #include <gromacs/xtcio.h>
      |          ^~~~~~~~~~~~~~~~~
1 error generated.
configure:5094: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "eprism3d"
| #define PACKAGE_TARNAME "eprism3d"
| #define PACKAGE_VERSION "1.2.6"
| #define PACKAGE_STRING "eprism3d 1.2.6"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "eprism3d"
| #define VERSION "1.2.6"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define STDC_HEADERS 1
| #define HAVE_PTHREAD_H 1
| #define HAVE_ZLIB_H 1
| #define HAVE__USERS_CHO_EPISOL_FFTW_3_3_8_INCLUDE_FFTW3_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <gromacs/xtcio.h>
configure:5094: result: no
configure:5109: checking for gromacs/fileio/xtcio.h
configure:5109: g++ -std=gnu++11 -c -g -O2  conftest.cpp >&5
conftest.cpp:52:10: fatal error: 'gromacs/fileio/xtcio.h' file not found
   52 | #include <gromacs/fileio/xtcio.h>
      |          ^~~~~~~~~~~~~~~~~~~~~~~~
1 error generated.
configure:5109: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "eprism3d"
| #define PACKAGE_TARNAME "eprism3d"
| #define PACKAGE_VERSION "1.2.6"
| #define PACKAGE_STRING "eprism3d 1.2.6"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "eprism3d"
| #define VERSION "1.2.6"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define STDC_HEADERS 1
| #define HAVE_PTHREAD_H 1
| #define HAVE_ZLIB_H 1
| #define HAVE__USERS_CHO_EPISOL_FFTW_3_3_8_INCLUDE_FFTW3_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <gromacs/fileio/xtcio.h>
configure:5109: result: no
configure:5295: checking that generated files are newer than configure
configure:5301: result: done
configure:5328: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by eprism3d config.status 1.2.6, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on chonoMacBook-Air.local

config.status:857: creating Makefile
config.status:857: creating config.h
config.status:1086: executing depfiles commands
config.status:1163: cd .       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
config.status:1168: $? = 0

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_c_compiler_gnu=yes
ac_cv_cxx_compiler_gnu=yes
ac_cv_env_CCC_set=
ac_cv_env_CCC_value=
ac_cv_env_CC_set=
ac_cv_env_CC_value=
ac_cv_env_CFLAGS_set=
ac_cv_env_CFLAGS_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CXXFLAGS_set=
ac_cv_env_CXXFLAGS_value=
ac_cv_env_CXX_set=
ac_cv_env_CXX_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_header__Users_cho_episol_fftw_3_3_8_include_fftw3_h=yes
ac_cv_header_gromacs_fileio_xtcio_h=no
ac_cv_header_gromacs_xtcio_h=no
ac_cv_header_inttypes_h=yes
ac_cv_header_pthread_h=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdio_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_unistd_h=yes
ac_cv_header_zlib_h=yes
ac_cv_objext=o
ac_cv_path_install='/usr/bin/install -c'
ac_cv_prog_AWK=awk
ac_cv_prog_ac_ct_CC=gcc
ac_cv_prog_ac_ct_CXX=g++
ac_cv_prog_cc_c11=
ac_cv_prog_cc_g=yes
ac_cv_prog_cc_stdc=
ac_cv_prog_cxx_cxx11=-std=gnu++11
ac_cv_prog_cxx_g=yes
ac_cv_prog_cxx_stdcxx=-std=gnu++11
ac_cv_prog_make_make_set=yes
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_CXX_dependencies_compiler_type=gcc3
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes

## ----------------- ##
## Output variables. ##
## ----------------- ##

ACLOCAL='${SHELL} '\''/Users/<USER>/Downloads/EPIPY-main/tutorials/episol/release/config_aux/missing'\'' aclocal-1.16'
AMDEPBACKSLASH='\'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMTAR='$${TAR-tar}'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='1'
AM_V='$(V)'
AUTOCONF='${SHELL} '\''/Users/<USER>/Downloads/EPIPY-main/tutorials/episol/release/config_aux/missing'\'' autoconf'
AUTOHEADER='${SHELL} '\''/Users/<USER>/Downloads/EPIPY-main/tutorials/episol/release/config_aux/missing'\'' autoheader'
AUTOMAKE='${SHELL} '\''/Users/<USER>/Downloads/EPIPY-main/tutorials/episol/release/config_aux/missing'\'' automake-1.16'
AWK='awk'
CC='gcc'
CCDEPMODE='depmode=gcc3'
CFLAGS='-g -O2'
CPPFLAGS=''
CSCOPE='cscope'
CTAGS='ctags'
CXX='g++ -std=gnu++11'
CXXDEPMODE='depmode=gcc3'
CXXFLAGS='-g -O2'
CYGPATH_W='echo'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
ECHO_C='\c'
ECHO_N=''
ECHO_T=''
ETAGS='etags'
EXEEXT=''
EXTRA_CFLAGS=' -I/Users/<USER>/episol/fftw-3.3.8/include'
EXTRA_LDFLAGS=' -L/Users/<USER>/episol/fftw-3.3.8/lib -lfftw3'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
LDFLAGS=' -lpthread -lz'
LIBOBJS=''
LIBS=''
LTLIBOBJS=''
MAKEINFO='${SHELL} '\''/Users/<USER>/Downloads/EPIPY-main/tutorials/episol/release/config_aux/missing'\'' makeinfo'
MKDIR_P='./config_aux/install-sh -c -d'
OBJEXT='o'
PACKAGE='eprism3d'
PACKAGE_BUGREPORT=''
PACKAGE_NAME='eprism3d'
PACKAGE_STRING='eprism3d 1.2.6'
PACKAGE_TARNAME='eprism3d'
PACKAGE_URL=''
PACKAGE_VERSION='1.2.6'
PATH_SEPARATOR=':'
SET_MAKE=''
SHELL='/bin/sh'
STRIP=''
VERSION='1.2.6'
ac_ct_CC='gcc'
ac_ct_CXX='g++'
am__EXEEXT_FALSE=''
am__EXEEXT_TRUE='#'
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__fastdepCXX_FALSE='#'
am__fastdepCXX_TRUE=''
am__include='include'
am__isrc=''
am__leading_dot='.'
am__nodep='_no'
am__quote=''
am__tar='$${TAR-tar} chof - "$$tardir"'
am__untar='$${TAR-tar} xf -'
bindir='${exec_prefix}/bin'
build_alias=''
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='${prefix}'
host_alias=''
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='${SHELL} /Users/<USER>/Downloads/EPIPY-main/tutorials/episol/release/config_aux/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
mkdir_p='$(MKDIR_P)'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/usr/local'
program_transform_name='s,x,x,'
psdir='${docdir}'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='${prefix}/etc'
target_alias=''

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "eprism3d"
#define PACKAGE_TARNAME "eprism3d"
#define PACKAGE_VERSION "1.2.6"
#define PACKAGE_STRING "eprism3d 1.2.6"
#define PACKAGE_BUGREPORT ""
#define PACKAGE_URL ""
#define PACKAGE "eprism3d"
#define VERSION "1.2.6"
#define HAVE_STDIO_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_STRINGS_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_UNISTD_H 1
#define STDC_HEADERS 1
#define HAVE_PTHREAD_H 1
#define HAVE_ZLIB_H 1
#define HAVE__USERS_CHO_EPISOL_FFTW_3_3_8_INCLUDE_FFTW3_H 1

configure: exit 0
