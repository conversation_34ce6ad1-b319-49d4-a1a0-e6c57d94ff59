{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["Welcome to the EPISOL Colab Playground!\n", "\n"], "metadata": {"id": "2UZUSEe9RzKv"}}, {"cell_type": "markdown", "source": ["![colab_logo.png](data:image/png;base64,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)"], "metadata": {"id": "JV402f06Qs2I"}}, {"cell_type": "markdown", "source": ["The 3D reference interaction site model (3DRISM) provides an efficient grid-based solvation model to compute the structural and thermodynamic properties of biomolecules in aqueous solutions, in this notebook we will walk through a high-throughput calculation on the solvation free energy of a test set of small molecules. We will compare our 3DRISM results to those of experimentally and computationally (FEP) determined free energies.   \n", "\n", "* **goals**:\n", "  * Generate the coordinate and topology files for our test set using RDKit and openFF\n", "  * Perform high-throughput 3DRISM calculations to determine solvation free energy of 100 molecules in the test set\n", "  * Be able to perform similar calculations on your own molecules\n", "    * use the colab-notebook to download and run calculations on your own molecules"], "metadata": {"id": "2JyzyqCTrO2i"}}, {"cell_type": "code", "source": ["#@title ##Download and Install Episol\n", "#@markdown ($\\approx 2$min) Stable as of 07/01/25 eprism v1.2.6\n", "%%capture\n", "import subprocess\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "#%cd ../home/\n", "%cd $HOME/\n", "%mkdir episol\n", "%cd episol\n", "!wget https://github.com/EPISOLrelease/EPISOL/raw/refs/heads/main/src/fftw/fftw-3.3.8.tar.gz\n", "!echo \"+++++++++++++++++++\"\n", "!echo \"downloaded fftw files\"\n", "!echo \"+++++++++++++++++++\"\n", "!tar -xzf fftw-3.3.8.tar.gz\n", "%cd fftw-3.3.8/\n", "#!./configure --prefix=/home/<USER>\n", "!./configure --prefix=$HOME/episol/fftw-3.3.8\n", "!make\n", "!make install\n", "%cd ../\n", "!wget https://github.com/EPISOLrelease/EPISOL/raw/refs/heads/main/src/kernel/release.tar.gz\n", "!echo \"+++++++++++++++++++\"\n", "!echo \"downloaded Episol files\"\n", "!echo \"+++++++++++++++++++\"\n", "!tar -xzf release.tar.gz\n", "%cd release/\n", "#!./configure --with-fftw=/home/<USER>\n", "!./configure --with-fftw=$HOME/episol/fftw-3.3.8\n", "!make\n", "!make install\n", "#%cd /content\n", "########################### WRAPEPR\n", "import subprocess\n", "import os\n", "import threading\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "!pip install episol\n"], "metadata": {"id": "e9Zwmfx1MLyE", "cellView": "form"}, "execution_count": 1, "outputs": []}, {"cell_type": "code", "source": ["%%capture\n", "\n", "#@title Install some python packages for topology generation\n", "#@markdown ($\\approx$4min)\n", "\n", "#@markdown This will prompt a restart in our colab session, this is necessary, just keep moving\n", "\n", "#@markdown (if you are using the notebook offline this wont be necessary, as presumably you'll have your own forcefield to generate topologies)\n", "########################################\n", "#        FOR COLAB USERS ONLY          #\n", "#---------------------------------------#\n", "# if you are running locally you dont need\n", "# condacolab. Just use your local conda dist\n", "########################################\n", "!pip install -q condacolab\n", "import condacolab\n", "condacolab.install()\n", "########################################\n", "#!conda update conda\n", "#!conda install --yes -c conda-forge  python=3.11 numpy=1.26.4 openmm pdbfixer parmed mdanalysis py3dmol rdkit openff-toolkit\n", "#!conda install -y -c conda-forge numpy=1.26.4 openmm=8.3.1 python={PYTHON_VERSION} pdbfixer=1.11 parmed=4.3.0 mdanalysis=2.9.0 py3dmol=2.5.2 rdkit=2025.03.5 openff-toolkit=0.17.0 libgcc\n", "!conda install -y -c conda-forge python=3.12 numpy=1.26.4 openmm=8.3.1 pdbfixer=1.11 parmed=4.3.0 mdanalysis=2.9.0 py3dmol=2.5.2 rdkit=2025.03.5 openff-toolkit=0.17.0 torchvision\n", "#openmm pdbfixer parmed mdanalysis py3dmol rdkitconda install libgcc"], "metadata": {"id": "Pz6E85eEOsUO", "cellView": "form"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["#@title import our download packages\n", "%%capture\n", "import py3Dmol\n", "\n", "def MolTo3DView(mol, size=(300, 300), style=\"stick\", surface=False, opacity=0.5):\n", "    \"\"\"\n", "    https://birdlet.github.io/2019/10/02/py3dmol_example/\n", "    Draw molecule in 3D\n", "\n", "    Args:\n", "    ----\n", "        mol: rdMol, molecule to show\n", "        size: tuple(int, int), canvas size\n", "        style: str, type of drawing molecule\n", "               style can be 'line', 'stick', 'sphere', 'carton'\n", "        surface, bool, display SAS\n", "        opacity, float, opacity of surface, range 0.0-1.0\n", "    Return:\n", "    ----\n", "        viewer: py3Dmol.view, a class for constructing embedded 3Dmol.js views in ipython notebooks.\n", "    \"\"\"\n", "    assert style in ('line', 'stick', 'sphere', 'carton')\n", "    mblock = Chem.MolToMolBlock(mol)\n", "    viewer = py3Dmol.view(width=size[0], height=size[1])\n", "    viewer.addModel(mblock, 'mol')\n", "    viewer.setStyle({style:{}})\n", "    if surface:\n", "        viewer.addSurface(py3Dmol.SAS, {'opacity': opacity})\n", "    viewer.zoomTo()\n", "    return viewer\n", "def smi2conf(smiles):\n", "    '''Convert SMILES to rdkit.Mol with 3D coordinates'''\n", "    mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "    if mol is not None:\n", "        mol = Chem.AddHs(mol)\n", "        AllChem.EmbedMolecule(mol)\n", "        AllChem.MMFFOptimizeMolecule(mol, maxIters=200)\n", "        return mol\n", "    else:\n", "        return None\n", "#free_energy(\"EXP\")\n", "!python -m ensurepip --upgrade # since we are using python 3.12 some pkg utils are now obsolete\n", "# after conda-initiate restart colab resets pip\n", "import matplotlib.pyplot as plt\n", "import openmm as mm\n", "from   openmm import app\n", "from   openmm.unit import *\n", "import py3Dmol as pymol\n", "import MDAnalysis as md\n", "import parmed as chem\n", "from openff.toolkit.topology import Molecule, Topology\n", "import numpy as np\n", "from MDAnalysis.transformations import center_in_box\n", "from episol import epipy\n", "from rdkit import Chem\n", "from rdkit.Chem import AllChem\n", "from openff.toolkit.topology import Molecule\n", "from openff.toolkit.utils import get_data_file_path\n", "from openff.toolkit.typing.engines.smirnoff import ForceField\n", "from openff.interchange import Interchange\n", "%cd /content/"], "metadata": {"id": "rjpO1aNMD_89", "cellView": "form"}, "execution_count": 7, "outputs": []}, {"cell_type": "markdown", "source": ["#**Walk Through Calculation:**"], "metadata": {"id": "MgcR5034eDJ8"}}, {"cell_type": "markdown", "source": ["* for this tutorial we will look at the solvation free energy of small molecules using FreeSolv Database\n", "* lets download our files"], "metadata": {"id": "M9LpAwuO4fNG"}}, {"cell_type": "code", "source": ["!wget https://github.com/MobleyLab/FreeSolv/raw/refs/heads/master/database.txt"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Uf5l6A2h4shQ", "outputId": "91ee626f-731d-40e2-ce4d-1c716d77629e"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["--2025-08-20 20:15:39--  https://github.com/MobleyLab/FreeSolv/raw/refs/heads/master/database.txt\n", "Resolving github.com (github.com)... ************\n", "Connecting to github.com (github.com)|************|:443... connected.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://raw.githubusercontent.com/MobleyLab/FreeSolv/refs/heads/master/database.txt [following]\n", "--2025-08-20 20:15:39--  https://raw.githubusercontent.com/MobleyLab/FreeSolv/refs/heads/master/database.txt\n", "Resolving raw.githubusercontent.com (raw.githubusercontent.com)... ***************, ***************, ***************, ...\n", "Connecting to raw.githubusercontent.com (raw.githubusercontent.com)|***************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 144897 (142K) [text/plain]\n", "Saving to: ‘database.txt’\n", "\n", "\rdatabase.txt          0%[                    ]       0  --.-KB/s               \rdatabase.txt        100%[===================>] 141.50K  --.-KB/s    in 0.004s  \n", "\n", "2025-08-20 20:15:40 (34.9 MB/s) - ‘database.txt’ saved [144897/144897]\n", "\n"]}]}, {"cell_type": "markdown", "source": ["* now we will make a list containing allof the SMILES strings and their corresponding energies"], "metadata": {"id": "zHzfYZ4dbftJ"}}, {"cell_type": "code", "source": ["line_count = int()\n", "experimental_values = []\n", "calculated_values = []\n", "smiles_list = []\n", "names_list = []\n", "\n", "with open('database.txt','r') as r:\n", "  for line in r:\n", "    line_count +=1\n", "    if line_count > 3:\n", "      tmp = line.split(';')\n", "      try:\n", "        names_list.append('_'.join(tmp[2].split()))\n", "        smiles_list.append(tmp[1].strip())\n", "        experimental_values.append(float(tmp[3].strip()))\n", "        calculated_values.append(float(tmp[5].strip()))\n", "      except Exception as exc:\n", "        RuntimeWarning(exc)\n", "smiles_list = np.array(smiles_list)\n", "names_list = np.array(names_list)\n", "experimental_values = np.array(experimental_values)\n", "calculated_values = np.array(calculated_values)\n"], "metadata": {"id": "ybOw1N1X5_xw"}, "execution_count": 3, "outputs": []}, {"cell_type": "markdown", "source": ["* lets look at one of our (randomly picked) molecules"], "metadata": {"id": "l6QiAYriXkZd"}}, {"cell_type": "code", "source": ["rng = np.random.default_rng()\n", "ind_to_extract = rng.integers(len(smiles_list),size=1)[0]\n", "test = smiles_list[ind_to_extract]\n", "\n", "mol = Chem.MolFromSmiles(test)\n", "mol = Chem.rdmolops.AddHs(mol,addCoords=True)\n", "mol"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 167}, "id": "FEwrT9HnXnvR", "outputId": "4af20bbd-2db6-41ba-fe28-5e8bc270d77d"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<rdkit.Chem.rdchem.Mol at 0x7ab288321380>"], "image/png": "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\n"}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "markdown", "source": ["* we can see our molecule has totally non-physical geometry\n", "* lets preform a simple geometry optimizationn step and view the output"], "metadata": {"id": "XyrBYVWJYxCi"}}, {"cell_type": "code", "source": ["AllChem.EmbedMolecule(mol)\n", "AllChem.UFFOptimizeMolecule(mol)\n", "mol"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 167}, "id": "C_L6NXvXYmTp", "outputId": "80fbc429-bafa-49f8-f6ec-753efadc9850"}, "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<rdkit.Chem.rdchem.Mol at 0x7ab288321380>"], "image/png": "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\n"}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "markdown", "source": ["* much better\n", "* now lets investigate solvation free energy of our molecule\n", "* First we need to make our topology file\n", "* thankfully using [openFF](https://openforcefield.org/force-fields/force-fields/) and [SMIRNOFF](https://docs.openforcefield.org/projects/toolkit/en/0.5.0/smirnoff.html) we can do this no problem!\n"], "metadata": {"id": "Dgn-b_jCZCit"}}, {"cell_type": "code", "source": ["# use openFF..toolkit to accept rdkit object\n", "from_rdmol = Molecule.from_rdkit(mol,allow_undefined_stereo=True)\n", "topology = from_rdmol.to_topology()\n", "# we will use openFF to assign SMIRNOFF parameters\n", "sage = ForceField(\"openff-2.0.0.offxml\")\n", "interchange = Interchange.from_smirnoff(force_field=sage, topology=topology)\n", "# just pick the first conformer\n", "interchange.positions = from_rdmol.conformers[0]\n", "#openmm_system = interchange.to_gromacs('out')\n", "openmm_system = interchange.to_openmm()\n", "#os.remove(\"out.top\")\n", "interchange.to_top(\"example.top\")\n", "tmp_u = md.Universe(mol)\n", "coords = tmp_u.atoms.positions\n", "# Buffer will be greater than 1nm (our default cutoff)\n", "# here we set to 7A on each side, so at least 1.4 nm apart from the other side of\n", "# the molecule\n", "buffer = 5  # convert to A\n", "#\n", "\n", "box_x = np.ceil(np.abs(np.max((coords[:,0]))-np.min((coords[:,0])))+buffer)\n", "box_y = np.ceil(np.abs(np.max((coords[:,1]))-np.min((coords[:,1])))+buffer)\n", "box_z = np.ceil(np.abs(np.max((coords[:,2]))-np.min((coords[:,2])))+buffer)\n", "tmp_u.dimensions = [box_x,box_y,box_z,90,90,90]\n", "trans = center_in_box(tmp_u.atoms,center='geometry')\n", "\n", "tmp_u.trajectory.add_transformations(trans)\n", "tmp_u.atoms.write(f'fixed_example.gro') # have to write out structure file\n", "## run 3DRISM\n", "test = epipy(f'fixed_example.gro','example.top',gen_idc=True)\n", "test.ndiis = 15\n", "test.delvv = 0.5\n", "test.r_c = 0.9 # cutoff at 0.9 nm instead of 1 nm (default)\n", "test.err_tol = 1e-12\n", "test.rism(resolution=0.5)\n", "test.kernel(nt=2)\n", "# We will use epipy's automatic unit conversion\n", "# to specifiy our free energy units\n", "test.free_energy()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vMfwLiXsQZON", "outputId": "2dc2cacb-5cf8-4dbd-cc90-f692cecd4828"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.12/site-packages/MDAnalysis/coordinates/GRO.py:444: UserWarning: Supplied AtomGroup was missing the following attributes: resnames. These will be written with default values. Alternatively these can be supplied as keyword arguments.\n", "  warnings.warn(\n"]}, {"output_type": "stream", "name": "stdout", "text": ["converted example.top to example.solute\n", "generated idc-enabled solute file to: idc_example.solute\n", "Calculation finished in 327 steps \n", "err_tol: 1e-12 actual: 9.25639e-13 \n"]}, {"output_type": "execute_result", "data": {"text/plain": ["-22.776747395965995"]}, "metadata": {}, "execution_count": 8}]}, {"cell_type": "markdown", "source": ["* our output energy is in units of kJ/mol\n", "* we can specify the units if need be by feeding our desired string to the function"], "metadata": {"id": "L4NprFSHgWaK"}}, {"cell_type": "code", "source": ["test.free_energy('kcal/mol')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "r4lVM13rgXtj", "outputId": "220a9128-4047-4451-dbc5-efa8a6ad6144"}, "execution_count": 9, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["-5.443773278194549"]}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "markdown", "source": ["* lets see the energy _per_ molecuule"], "metadata": {"id": "WEVBC7myg--U"}}, {"cell_type": "code", "source": ["test.free_energy('kcal')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YlICzjTqhEAs", "outputId": "4104c41d-1ff6-4c2d-a0aa-3066971daa82"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["-3.2782402681287574e+24"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "markdown", "source": ["* now lets run the first N molecules in the test set"], "metadata": {"id": "rqJZiYughRX3"}}, {"cell_type": "code", "source": ["number_of_molecules_to_use = 50\n", "\n", "rng = np.random.default_rng()\n", "ind_to_extract = rng.integers(len(smiles_list),size=number_of_molecules_to_use)\n", "\n", "#smiles_and_names = dict(zip(names_list[ind_to_extract],smiles_list[ind_to_extract]))\n", "smiles_and_names = dict(zip(names_list[:number_of_molecules_to_use],smiles_list[:number_of_molecules_to_use]))\n"], "metadata": {"id": "8JT1Hp1CVj3f"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["len(smiles_and_names)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9EFUxqMAgPcS", "outputId": "7665c30a-dcff-4f62-f1b0-7254c349bc12"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["50"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "markdown", "source": ["Run !\n", "$\\approx$ 5min for the first 50"], "metadata": {"id": "WVxEh5z9u5fC"}}, {"cell_type": "code", "source": ["%%capture\n", "#mols = [Chem.MolFromSmiles(x) for x in smiles_list[:5]]\n", "\n", "def generate_topology_and_get_energy(smiles_list:list,keep_files=False):\n", "  \"\"\"\n", "  This will automatically generate topology files for\n", "  any small molecule SMILES-string.\n", "  !!! Warning !!!\n", "  We are using undefined stereochemistry\n", "  -----------\n", "  input: smiles_list -> list of SMILES strings\n", "  output: energy_list -> list of solvation free energies whose\n", "  index corresponds to the SMILES string input\n", "  keep_files: boolean -> do you want to save ALL file generated\n", "  or just keep the energy\n", "  \"\"\"\n", "  import os\n", "  out_energies = []\n", "  try:\n", "    for name in smiles_list:\n", "      stage = \"convert to rdkit mol\"\n", "      mol = Chem.MolFromS<PERSON>(name)\n", "      mol = Chem.rdmolops.AddHs(mol,addCoords=True)\n", "      stage = \"Add hydrogens and Geometry optimize \"\n", "      # geometry optimization step\n", "      AllChem.EmbedMolecule(mol)\n", "      AllChem.UFFOptimizeMolecule(mol)\n", "      ##\n", "      stage = \"Pass to openFF \"\n", "      # use openFF..toolkit to accept rdkit object\n", "      from_rdmol = Molecule.from_rdkit(mol,allow_undefined_stereo=True)\n", "      topology = from_rdmol.to_topology()\n", "      # we will use openFF to assign SMIRNOFF parameters\n", "      stage = \"Use smirnoff \"\n", "      sage = ForceField(\"openff-2.0.0.offxml\")\n", "      interchange = Interchange.from_smirnoff(force_field=sage, topology=topology)\n", "      # just pick the first conformer\n", "      interchange.positions = from_rdmol.conformers[0]\n", "      #openmm_system = interchange.to_gromacs('out')\n", "      openmm_system = interchange.to_openmm()\n", "      #os.remove(\"out.top\")\n", "      stage = \"Generate topology\"\n", "      interchange.to_top(\"out.top\")\n", "      tmp_u = md.Universe(mol)\n", "      coords = tmp_u.atoms.positions\n", "      # Buffer will be greater than 1nm (our default cutoff)\n", "      # here we set to 7A on each side, so at least 1.4 nm apart from the other side of\n", "      # the molecule\n", "      buffer = 5  # convert to A\n", "      #\n", "      stage = \"Center our molecule in a PBC\"\n", "      box_x = np.ceil(np.abs(np.max((coords[:,0]))-np.min((coords[:,0])))+buffer)\n", "      box_y = np.ceil(np.abs(np.max((coords[:,1]))-np.min((coords[:,1])))+buffer)\n", "      box_z = np.ceil(np.abs(np.max((coords[:,2]))-np.min((coords[:,2])))+buffer)\n", "      tmp_u.dimensions = [box_x,box_y,box_z,90,90,90]\n", "      trans = center_in_box(tmp_u.atoms,center='geometry')\n", "\n", "      tmp_u.trajectory.add_transformations(trans)\n", "      tmp_u.atoms.write(f'fixed_mol.gro') # have to write out structure file\n", "      stage = \"Start 3DRISM Calculation\"\n", "      ## run 3DRISM\n", "      test = epipy(f'fixed_mol.gro','out.top',gen_idc=True)\n", "      test.ndiis = 15\n", "      test.delvv = 0.5\n", "      test.r_c = 0.9 # cutoff at 0.9 nm instead of 1 nm (default)\n", "      test.err_tol = 1e-12\n", "      test.rism(resolution=0.5)\n", "      test.kernel(nt=2)\n", "      # We will use epipy's automatic unit conversion\n", "      # to specifiy our free energy units\n", "      out_energies.append(test.free_energy('kcal/mol'))\n", "      # get rid of our calculation files\n", "      # we dont really care about saving them\n", "      # this way we dont have files piling up\n", "      if not keep_files:\n", "        os.remove(\"out.top\")\n", "        os.remove('fixed_mol_out.ts4s')\n", "        os.remove('fixed_mol_out.log')\n", "  except Exception as exc:\n", "    print(f\"molecule: {name} failed at stage {stage}\")\n", "    print(RuntimeError(exc))\n", "  return out_energies\n", "# Now we run\n", "out_energies = generate_topology_and_get_energy(smiles_list=[smiles for smiles in smiles_and_names.values()])"], "metadata": {"id": "F0hi1ewXLmMw"}, "execution_count": 13, "outputs": []}, {"cell_type": "markdown", "source": ["* now, lets see our results"], "metadata": {"id": "VCwEDJIDKDzz"}}, {"cell_type": "code", "source": ["fig,ax = plt.subplots()\n", "\n", "#out_energies = np.copy(np.array(out_en))\n", "dslice = len(out_energies)\n", "ax.scatter(experimental_values[:dslice],out_energies,label='3DRISM')\n", "ax.scatter(experimental_values[:dslice],calculated_values[:dslice],label='FEP')\n", "#ax1.scatter(calculated_values[:dslice],out_energies)\n", "#ax1.set_xlabel(\"experimental values\")\n", "ax.set_ylabel(\"$\\\\Delta G_{solv}$ / kcal/mol\")\n", "ax.set_xlabel(\"experiment / kcal/mol\")\n", "vv = np.arange(min(experimental_values[:dslice]),max(experimental_values[:dslice]))\n", "ax.plot(vv,vv,'k--')\n", "ax.set_ylim(-15,None)\n", "ax.set_xlim(-15,None)\n", "ax.legend(loc=\"lower right\")\n", "fig.tight_layout()"], "metadata": {"id": "YGh9CUYpbljn", "colab": {"base_uri": "https://localhost:8080/", "height": 487}, "outputId": "faa6fee0-0b7c-4624-f7b5-6376c21ce5bf"}, "execution_count": 14, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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******************************/zxRxQWFuKll17S5+2QCTCwMyMGdkREhjPH7hCWwOSjiKlbFGvqtMgtEJjw8wNEjnsTz05cYLzXJaPgzhNERGR1zLU7hCVQ1rl7rnV9hDWuY/yp4QoyZQ9dKUGrL+9hQ2oJxs1bgYIC09Tyo+rBwI6IiCyCPrtDWBOZXCDp4k1sS7mOpIs3zROYBoUr1tCVSrookQvE7C3AU2vycTlXoKG3A7b+uA3OztVXy4+Mj6ktRERkEcy9O4QpaF0/92xTRLhl6L5GrqqkdkDEIkX2KyT4+7YMw7Y+wOFrihp2L7d0wOcr1sEj9EnT9YGqBQM7IiKyCJa4O0RVaFsv2OruAbTcMgYovT2YRz1F4KUtq9UYggcAL6zDtU3T0PrLi7hbBHg6AcsH10Pk28tN+9pUbTgVS0REFsESd4cwlLb1gn2kyfjCYQn8UWY6WUMtOZMIHoBH5pzHkOeeQec2zXDq982IXHeFQZ0NsfnAbtmyZWjQoAGcnZ3RsWNHJCcnV9j++++/R7NmzeDs7IwWLVrg559/rqaeEhHVbBXtDgEo1ti91OHRau2ToTStF5RCjhiHdYr/LvcGH4aACTMr3eLLEIcOHUJWVtbDjtjh87VbsDc5FUGdh5h2CpiqnU0Hdt999x2mTZuGmJgYnDhxAq1atUKfPn2Qk5OjsX1iYiIiIyMxevRonDx5EgMHDsTAgQORlpZWzT0nIqqZtO0OofTJ73+i86I9SEjLrLY+GZL8oGkdYKj0HOpJbmkI6pQEkHddUZ/OSEpKSjB79mw89dRTGDVqFORyOQDAxcWFO0jYKJuuY9exY0d06NABn3/+OQBALpcjMDAQr7/+OmbOnFmu/Ysvvoj79+9jx44dqmOdOnVC69at8eWXX+r0mqxjR0RUdTK5wOd7/sInv/9V7rnqrGlnaLHkpIs3EbnisOqxFHJMsd+CSfY/Vf6ig1YCLQZXpdsAgL///hvDhg3D4cOKfkRFReGrr75i1qsVYh07AEVFRTh+/Diefvpp1TGpVIqnn34aSUlJGs9JSkpSaw8Affr00dqeiIhMZ9PRqxqPV1dNO2XyQ9kp1azcAkTHnahw1LD0esEI6WEkO0XrFtQBiixZuUyxW0TqFsV3PaZnhRD49ttv0bp1axw+fBienp7YuHEj1q5dy6CuBrDZcdh///0XMpkMdeuqF2WsW7cuzp07p/GcrKwsje1V6xI0KCwsRGFhoepxXl5eFXpNRESAfjXtwhrXMfrrV1YsWQJFYNkr2F9jMWHlesFLG9/Eq/Y7INGh3rBcAIWu/nDJv1l+X1cds2bz8vLw6quvYtOmTQCAzp07Iy4uDkFBQZV3gGyCzY7YVZcFCxbA09NT9RUYGGjuLhERWT1z17QzRrHkCMkRvOqwQ3MmSBnKgccthZ0gvh+pHtQBOmfNSqVSHD9+HHZ2dpg3bx727dvHoK6GsdnAzsfHB3Z2dsjOzlY7np2dDX9/f43n+Pv769UeAGbNmoXc3FzV19WrmqcOiIhId7rWqvv3bqFJdnSocmAplwE734AEOsV1yEIdTCiehJ6yA0BFm6ppyJotKSlRJUW4ublh06ZNOHToEN555x3Y2THjtaax2cDO0dER7dq1w+7du1XH5HI5du/ejbCwMI3nhIWFqbUHgF27dmltDwBOTk7w8PBQ+yIioqqprKYdoCgZMm/nWUzelILIFYd1z5bVYf1alYslX04E8v/V6RqfFQ9E58JPcQceqCe5VcF7Lp81e/HiRXTu3BmfffaZ6ljbtm3RsWNHnV6bbI/NBnYAMG3aNKxYsQJr167F2bNnER0djfv372PUqFEAFBlCs2bNUrWfPHkyEhIS8NFHH+HcuXOYM2cOjh07hokTJ5rrLRAR1UiV1bQD/pu+VNIlqQHp2xXr19Y+C/wwWvF9SUi5Kc4qF0u+l635uAaJIgRySOGHO7qdcC8bQgisW7cOrVu3xpEjR7Bw4ULcv39f59ck22XTgd2LL76IxYsXY/bs2WjdujVSUlKQkJCgSpC4cuUKMjP/+wUQHh6ODRs24Ouvv0arVq2wZcsW/PTTTwgJCTHXWyAiqrG01bTTVgeu0mzZ9O2KdWo6rF+rKLBUPo7pH6wxcQKAIrNVB/8KDyTLm0EKOXwkd3Q6547cFUOHDsWIESNw7949dOnSBUeOHEGtWrV0Op9sm03XsTMH1rEjIjIumVwgOeMWcu4W4N+7hZi382yl52wc20k9W1YuK59pqkaiyDydkqq2E4Ohdewqez3lX97o4kkQkCLGYR3qSbQnYij9keWK4b844sqVK7Czs0NsbCxmzpzJtXQ2Tp/YwmbLnRARkW2wk0pUQdq2lOs6nVMuqeFyYgVBHaC2fq1hF9XRiJAA9Ar2VwWWfu6K6VetI3VKUjtFeZLNUdCYDCEBvip+FgJSLHdYolOGRfY9OXqvzEZBiUCjRo2wYcMGrqWjchjYERGR1TA4qUHXNW8a2tlBjjBpOmCXDUjrAggHoMMIWfAA4IV1QMIM9aDS1QfyZxajldOTGPFDZ0gKdMucresmxZyujjjr1R1LV2+Gu7u7bu+JahQGdkREZDWUSQ1ZuQUai4JIAPhrSmrQcc1buXbp28sHZjoWCwagaNOsn2Ik8F624vpB4bADELbtdaBAe8AphMC3p4vRqq4dWvkrAsm3nnSEZPBogEEdaWHTyRNERFQ9ZHKBpIs3TVJTrjSDkxqCwhUBWUV5rh71Fe2U9Ei2qJDUTjG922Kw4vu5ncCHTYBT67WecqdAIPKHBxjxUwGGbn2AB8WK+ymRSHQPUqlG4ogdERHprHQig3K92a70LMMSDAykzJYt+5r+Fb2m2po3CdTXvT0M9iIW/pc4IZcpRuoq2lQsYaZiNE6qR+KCMljUeF2FPy6XYPiPD3AlV8BOAgwNcYCj3cN+etRTDz6JymBWrJExK5aIbJWmDFEvVwfcyS8u11Y5LrZ8eFuTBHeA5iCz9EidxufPxWuYWq2vCOpKT61m/KGocVeZETvUki0qVEmmbLFMYO7+Qrx/sAhyATSuLcH6/7mg4yP2UN3RF9bpNgVMNoVZsUREZFQJaZmIjjtRbpxJU1AHqMa0EBufjl7B/pVnkRqgdLZsWdrLlHRAxJS0cmveyo26VSHZQqsKMnNv5svRb8MDHLmu2AVjZGsHfBbhDHenh/fNo1754JNIAwZ2RERUIZlcIDY+vYLJQ80EgMzcAiRn3NIagJmCtiBUuTOFYhSxklE2Q5MtKlJBEFjbRQJXB8DTCfjqWRe8GOIAtB4KNO6pPfgk0oCBHRERVSg545bayJe+ytWUM6GKglC9RhGVyRZ5mdC8Hs6A9W5lgsA7BQKOdoCrgwRSiQTfPu8CmQAe9ZQC7vWAAZ8zmCO9MSuWiIgqVNXATNfac8ZQWRBaehSxQspkCwBa829LJ1voolRm7h+XS9Dqy3uY/tt/fa3vIVUEdQDQdxGDOjIIAzsiIqqQoYGZBIp1beVqypUmlykSFVK3KL7LZYZ18iFdg1Cd2ikLDHuUSf7wqGdYEoPUDsU95+PdPQXotjYfV3IFfr1YgrzCUiOCLt7AC99yLR0ZjFOxRERUocqKAmtSYU05paoW/9XA4J0ptNFSYNiQ0bSLFy9i2Ksf4MiRQgBlEiRcvIGO44Gn3uRIHVUJAzsiIqqQsihwdNwJjRXgBMqXPamwphygvZ6bsvivgWU9DN6ZoiLKAsMGEkJg3bp1mDhxIu7duwdPT098tXw5Xuz0SJWDRaKyWMfOyFjHjohslfYSIsHoFexfYU05NZXUc1MlJkxJNSjYUWbFAhrLEJu0tp4mN2/exGOPPYbbt2/jqaeewrfffotHH3202l6frJ8+sQUDOyNjYEdEBpPLjDLlZ0qVFQXWiSmK/5ZRURBanUGd0o8//oj09HTMnDkTdnaW9TMly8fAzowY2BGRQUyw3sxipW4BfhhdebunpgO+zYBavoAQQP6/egW8RglCDVBcXIzY2FiEhoZiwAAb+9mRWXDnCSIia2Ki9WYWS9eivgc+1Hxcx4C3op0pTOXixYsYNmwYjhw5gjp16uDChQvw8vKq1j5QzcZyJ0RE5lTpZvNQbDZfxTIgFqVUPTeDPAx4ZWe2IeniTWxLuY6kizchk5tvAkoIgbVr16J169Y4cuQIvLy88MUXXzCoo2rHETsiInOqYP9QBQHkXVe0q0JmpkVRFv/dHAWUy7PVhYCABP98PxXDCj6F/OEYhbnW0N2+fRvR0dH47rvvAIAJEmRWHLEjIjInU2w2bw20Ff/VkQQC/riJUOk51THlXrAJaZnG6mWlbt++jdatW+O7776DnZ0d5s+fjz179jCoI7PhiB0RkTmZYrN5a1G2+G/OOeAPLevqtPDDHdV/67UXrJHUrl0bffr0wZ49e7BhwwaEhoaa/DWJKsLAjojInEyx2bw1KV38N+MPvQO7HHipPS69F6ypEicuXLiAWrVqISBAMdr4ySefQC6Xw93d3SSvR6QPTsUSEZmTKTabt1Z6JFXIBXBD1EGyvJnG53XdM1YfQgisWbMGbdq0wYgRIyCXywEAtWrVYlBHFoOBHRGRuRl7s3lrVWGQ+x9l8mts8cuqxImyfNycjNq127dv46WXXsKoUaNw7949FBYWIi8vz6ivQWQMnIolIrIERtxs3qopg9yyxZpLyUIdxBa/jF/lFaxnM2LlkwMHDmD48OG4evUq7O3tERsbixkzZnAHCbJIDOyIiCyFHpvNm2tXhWpRNsgttfPEwSw7RO221zpSp/Tv/cIqd6O4uBhz5szBggULIIRAkyZNsH79eiZIkEVjYEdEZGUsbR9Uk9AS5Nq53oR89+FKT/dzd65yF4qKirBlyxYIITBq1Ch89tlncHNzq/J1iUyJa+yIiKxIQlomouNOqAV1gHlquJlDaENvBHg6a12BJ4EiyA1t6G3Q9YUQUG6hXqtWLWzYsAGbN2/GqlWrGNSRVWBgR0RkJWRygdj49Io2H0NsfLpZt9YyNTupBDH9gwFozSFGTP9gg6allQkSH3/8sepYu3btMGTIEAN7S1T9GNgREVmJ5Ixb5UbqSitdw82WRYQEYPnwtvD3VJ9u9fd0xvLhbQ2ajj5w4ABatWqFzZs3Y/bs2fj333+N1V2iasU1dkREVkLX2mymqOFmaSJCAtAr2L/KCSTaEiR8fHxM1HMi02JgR0RkJXRNCDBG4oA1sJNKqrS7xIULFzB06FAcPXoUAPDKK6/g008/5Vo6smoM7IiIrIQycSArt0Db5mPwr0LiQE2Sl5eHjh074tatW/Dy8sLXX3/NtXRkE7jGjojISpgycaCm8fDwwKxZs9C1a1ecPn2aQR3ZDIlQ5nWTUeTl5cHT0xO5ubnw8PAwd3eIyAbViDp2JrB//354enqidevWAAC5XA4hBHeQIIunT2yh81TstGnTdO5A6VRxIiIyLmMlDtQUpRMkHn/8cZw4cQKurq6QSjlpRbZH58Du5MmTOrWTSPiLhYjI1KqaOGBO1bkdWtkEiSeffBKcqCJbpnNgt3fvXlP2g4iIaoDqmkYWQmDNmjV4/fXXcf/+fXh5eWHFihUYPHiw0V6DyBJxHJqIyMbI5AJJF29iW8p1JF28aTE7UVTXdmj5+fl48cUX8corr+D+/fuqBAkGdVQTGBzY3blzBx999BHGjBmDMWPG4OOPP0Zubq4x+2awS5cuYfTo0WjYsCFcXFzQuHFjxMTEoKioqMLzunXrBolEovY1fvz4auo1EVHVJaRlovOiPYhccRiTN6UgcsVhdF60x+x7yFbndmjOzs64ffs27O3t8f7772P37t0IDAys8nWJrIFBdeyOHTuGPn36wMXFBaGhoQCATz75BO+//z5+++03tG3b1qid1Ne5c+cgl8vx1VdfoUmTJkhLS8PYsWNx//59LF68uMJzx44di7lz56oeu7q6mrq7RERGoRwRKxsaKUfEDN1uyxj02Q5N57WDchlwORG4l41ipzooqd8BLrXcIJVKsXbtWly7dk31N4qopjAosJs6dSoGDBiAFStWwN5ecYmSkhKMGTMGU6ZMwYEDB4zaSX1FREQgIiJC9bhRo0Y4f/48li9fXmlg5+rqCn9/f1N3kYjIqCobEZNAMSLWK9jfLNmzRt8OLX07kDADyLuBC7fkGPpDPtoGeeDLb9YCwQNQr1491KtXrwo9JrJOBk3FHjt2DDNmzFAFdQBgb2+Pt956C8eOHTNa54wpNzcX3t6VV2NX7hEYEhKCWbNmIT8/v8L2hYWFyMvLU/siIqpu+oyImYNRt0NL3w5sjoLIvY7VJ4vQ+st7OHpDjs0nc5G9arjieaIayqDAzsPDA1euXCl3/OrVq3B3d69yp4ztwoULWLp0KV599dUK2w0dOhRxcXHYu3cvZs2ahW+//RbDhw+v8JwFCxbA09NT9cV1HERkDkYfETMy5XZo2sYKJVBkx1a6HZpcBiTMwO0Hcry45QFe2V6A+8VAtwZ2ODW+Fuq6SYGEmYp2RDWQQYHdiy++iNGjR+O7777D1atXcfXqVWzatAljxoxBZGSksfuoMnPmzHLJDWW/zp07p3bO9evXERERgSFDhmDs2LEVXn/cuHHo06cPWrRogWHDhmHdunX48ccfcfHiRa3nzJo1C7m5uaqvq1evGuW9EpFtMlXGqlFHxEzAaNuhXU7E/tNX0PLLe/g+vQT2UmBBTyf8/rIrAj2lAASQd12x9o6oBjJojd3ixYshkUgQFRWFkpISCCHg6OiI6OhoLFy40Nh9VHnjjTcwcuTICts0atRI9d83btxA9+7dER4ejq+//lrv1+vYsSMAxYhf48aNNbZxcnKCk5OT3tcmopqnqjXcKirsqxwRy8ot0LjOTgLAX5cRMROKCAnA8uFty90Dfz3uQf4/VzDk+wf4J1+gibcUG/7ngg71NWwJdi/bmF0nshpV2is2Pz9fNZrVuHFji8ogvX79Orp374527dohLi7OoL0ADx06hM6dO+PUqVNo2bKlTudwr1gi0kRbxqpyfKqyjNWyQaEUckS4/41X27iiVfNmQFA4EtJzEB13AgDUXkfX16guVdp5IuMP/DSrD+LPl+DTvs5wc9Ry3ogdQMMuxus0kRnpE1sYHNgVFBTg9OnTyMnJgVwuV3tuwIABhlzSaK5fv45u3bohKCgIa9euVQvqlBmv169fR8+ePbFu3TqEhobi4sWL2LBhA5555hnUqVMHp0+fxtSpU/HII49g//79Or82AzsiKksmF+i8aI/W5AblaNrBGT00Bjhlg8I+0mTEOKxDPUmpRAiPekDEIiTIO1TLzg6moCngk0qA1atXo06dOnjuuecUa+eWhAB5mYC2sUmPesCUVMXDh+VQ4FYXCAoHpPr/I5/I3PSJLQyaik1ISMDLL7+MmzdvlntOIpFAJjPvotVdu3bhwoULuHDhAh555BG155RxbHFxMc6fP6/KenV0dMTvv/+OJUuW4P79+wgMDMSgQYPwzjvvVHv/ici2VKWGW9kyJn2kyVjusKT8NfIyIdkchYgX1qHXjP7VthersWiapvZ1LIbz4ZU4uGsHvL29cebMGcU/ziMWAZujoAiJNYxNRiwEzu1UlUNReRj8Iti8gw9EpmTQiN1jjz2G3r17Y/bs2ahbt64p+mW1OGJHRGVtS7mOyZtSKm336Uut8Vzr+mrHki7eROSKwwAU068HnSbBH7egKU4TkKDI1R/209JgZ2/Qv9vNQtM0dcGV0/h3x8eQ3f0Xdvb2mP/ee3jzzTf/m4EpVcdOxaO+IqgDHgZ+Wia+X1jH4I6sislH7LKzszFt2jQGdUREOqhKxmrp8iSh0nPq069lSCDglJ+JCYuWYcBzL1j81CtQfkRSyEpw52Ac8g7/AEDAvnY9NIv8P7w5PVp91DF4ANCsX/mpVkAxVVtRqeaEmYpzOS1LNsigcieDBw/Gvn37jNwVIiLbVJUabqWDPT/c0en17O8rkijMvT+sLkpPU8uLC5G1fjryDm8BIODWsjcCRn6Ku+5BmgsrS+0UCRItBiu+S+0UgV7pUbxyWA6FbJtBI3aff/45hgwZgj/++AMtWrSAg4OD2vOTJk0ySueIiGyBsoZbdNwJbavCtNZwK13GJAdeOr2esl1sfDp6NKuL45dvW+x6u9IjklIHJzjWbYKS25nwjngdtZo+qbFdhXQtc8JyKGSjDArsNm7ciN9++w3Ozs7Yt28fJJL/fklIJBIGdkRkVlUqp2EihtZwKx0UHpU3ww3hrX2NnQBuwgPH5I+rEjI6LdiNW/eLVG0sLUPWWfYAsnu3YedWGwBQu8doeIa9CHsPH7V2OhdWdtNxiZCu7YisjEHJE/7+/pg0aRJmzpwJqdSg2VybxeQJIvOqahFgUzM06FS+r5Z3D6iyYrWddkN4I7Y4Cr/KQyGFHKHSc/DDHeTAC0flzSCH1CJq2u3btw8vv/wy8px8UXtQLCAp//ekwlIwcpn2NXa6lEPhGjuyEiavY+ft7Y2jR49q3Y2hJmNgR2Q+VS0CbOlkJSU4d+RXOP2dgPpX4uFSfFtjO+UuZfGyTnjKLg21JfdUz90Q3phbHIVT7k9prZtXIU3BlJ4BUlFREWJiYrBo0SIIIVA/qBEk/WJg715H98LKGrNiH5YzAR5mxQIaJ76ZFUtWRp/YwqDhthEjRuC7774zqHNERKZQNruyNOWx2Ph0o+3NWu3St8PusxZ4YtdQNLm4Di7FtyGDBJrejlSiCGGesz+sFtQBgD9u4QuHJWh594DmhIRK+oAlIcDaZ4EfRiu+LwlRHNfRX3/9hSeffBILFy6EEAJjxozBubRTWBHdC/6e6tOt/p7O2oO6zVHlkyTybvwX0L2wDvAoc55HPQZ1ZPMMWmMnk8nwwQcf4Ndff0XLli3LJU98/PHHRukcEZGuqlIE2OIpA5kyYasdBLSl2kq0HJdKFCN6MQ7f4mjeaAA63gstfUBepuJ4JQGTEAKrV6/GpEmTcP/+fdSuXRsrVqzAoEGDAAARIW7oFexf+TS1XKYYqdMYwkNxPGGmYqpVUzkUTr+SjTMosEtNTUWbNm0AAGlpaWrPSbT9NiEiMiFdsyZ1zq60BHIZcOkgEP86tAcy+pNKgHq4iSb5qQAe1a0fWoMp3WrDFRYW4sMPP8T9+/fRvXt3rFu3rtzOQHZSSeVBd6XlTPBfOZOGXbhfLNU4BgV2e/fuNXY/iIiqpCpFgC2SpjVkRtbcPV+3hvrUhtMSSDk7O2PDhg347bff1HeQ0NddHWvz6dqOyMbotcZu9uzZOH78uKn6QkRksKoUAbY42taQGZnU3V+3hrrWfMvYD6RuATL+QFHBA8yaNQuLFy9WPd2mTRvMmDHD8KAOAO7/Y9x2RDZGrxG7a9euoW/fvnB0dET//v0xYMAA9OzZE46OjqbqHxGRTqpSBNiiVLqGrOoEAIlH/f/Kg1RG15pvBz4EAPx5U4Zh22Q4drUADg4OGDJkCIKCgio/X5eM21q+uvVF13ZENkavEbtVq1YhKysLGzduhLu7O6ZMmQIfHx8MGjQI69atw61bemZYEREZkbIIsM7ZlZZIlzVkZWmo/6aNYkWcBIhYqHsiQVC4IqNU63jow2sLgZUnitDmq/s4drUAtZ0l2PjBNN2COl0zbt11/Bnq2o7IxhhUx660s2fPIj4+Htu2bcPx48cRGhqKAQMGIDIyEvXr1zdWP60G69gRmZ8l7jyhs9QtisBGJw/f05A1gGsdxUjXzYvAvgUPn9fw693FG+j/qf4lP1RZsZqve+uBwLj4B/jhbAkAoHsDO6x73lWRIFFZMWBtGbea6s7JZQ8LEFcQ/HrUZwFisikmL1CsTU5ODuLj47F9+3Z06dIFb775prEubTUY2BFRlWT8oRit0oVHfcXIW9kgTVPihUttoGM08NSbhgc8WhI6CksEgr+4h79vC9hLgfe6O+HNcMf/gukRO7Rnp1YaqGnYKUJrkMkCxGSbzBbYEQM7IqoiVaCjbUssKEbdBq9WBEvagjQj7BBR2XXlOWch/UORHPFxUiG+PFaMDYNc0L5emdcZtBJoMVjz9XQNZMsGhxp3ntAS6BJZOX1iC52TJ6ZNm6ZzB1igmIjIQFI7xbZYm6MAbWkg/T8FGner/DqmqOH28Lrf7DiI3xKSsdlHcXhKJ0e82s4RtRw1THlXlHyha8Zt2XbBA1iAmEgDnQO7kydPmrIfREQWq9rX7AUPUEwnatwLVYcRKVON1kGRIDFt3sf49L3/g51bHfz1qhcaO96BVCJBrXIFEh5Oo1aUfatrxq2mdqYKXomsmM6BHYsSE1FNlJCWidj4dLXtygI8nRHTP9i0WbaGjkhpnKKspxgFrOIU5a1btzB27Dhs3foDAMDO3Qdz85/GWsdvIBeKHS2UhCL3tvLsW2XGrZapZwEJHrjUxamSpgiVC+tJgiEykyqtsUtPT8eVK1dQVFT03wUlEvTv398onbNGXGNHZDsS0jIRHXdCW66m5ZVQ0Se7VE979+7Fyy+/jOvXrwNSO3g99TI8Qv8HiUSKPtJkxDisQz3JfyWvCl0D4PTsB7q9npZkCPnDh6tkEfhd3h5X3Vrh3QEtLOueE1UDkydP/P3333j++eeRmpoKiUQC5SWU+8TKZDIDum0bGNgR2QaZXKDzoj1qI3WlSaCoj3dwRg/LGEUyJLtUByUlJXjnnXfwwQcfQAiBekGNIO82CU7+TdTaSSFHqPQc/HAHOfBC5JAX8VwbHfahVdIw0igTUthJ5KrHN4Q35hZHYeDQ8QzuqEbRJ7bQq0Cx0uTJk9GwYUPk5OTA1dUVZ86cwYEDB9C+fXvs27fPkEsSEVmU5IxbWoM6QDGulJlbgOQMCynMrs9+rnqQSqU4efIkhBAYM2YMvo3fWy6oAwA5pDgsD8Z2eTgOy4Ph51FLv/4HDwCmpEEWFY+N0mchBCCBXK2JP27hC4cl2PfTKsjkLOhApIleW4opJSUlYc+ePfDx8YFUKoVUKkXnzp2xYMECTJo0iYkWRGT1cu5qD+oMaWdyhmaXaiCEQElJCRwcHCCVSrFmzRocPnwYzz//PGRygQDPv5CVW6CxGItyJNOgPXmldkiWNUNXWSIE1Nfs4eFjuQAmFa9E8sWxCHvMT//XILJxBo3YyWQyuLu7AwB8fHxw44biX4lBQUE4f/688XpHRGQmfu7OlTfSo53JVSW7tJRbt25h8ODBmDBhgupYQEAAnn/+eQD/7ckLlN9gzBh78souHUI9ya1yQZ2SVALUk9yE7NIhg65PZOsMCuxCQkJw6tQpAEDHjh3xwQcf4NChQ5g7dy4aNWpk1A4SEZlDaENvBHg6a90dVQJFdqxBI1M6kskFki7exLaU60i6eLPi6cdK93OVKAr4VlB6ZO/evWjZsiW2bt2KtWvX4sKFCxrbmXJPXj/JHaO2I6ppDJqKfeedd3D//n0AwNy5c/Hss8+iS5cuqFOnDr777jujdpCIyByUI1PRcSe0lQk2bGRKxxpzepdZ0aWwsZbSI0VFRZg9e7YqQeLxxx/Hhg0b0KRJ+bV0ShEhAegV7G/0+n6NGzUGDurYjojKMdqWYrdu3ULt2rVVmbE1FbNiiWyLUevY6VhjrkplVvTcauvPP//E0KFDcfz4cQDA2LFj8cknn6BWLT2TH4xFLsODD4PhlJ+lcTpWLoBCV3+4TE/nLhNUY3CvWDNiYEdke4yy84SONeaMUmZFx1HBkpISPPbYY7h06RK8vb3xzTffqNbSmVX6dojNURAQauuF5AAkkEBShXp8RNbI5OVOFixYgFWrVpU7vmrVKixatMiQSxIRWSw7qQRhjevgudb1Eda4jmHTrwkzoGlnBdWxhJmAXGacMivKrbZaDFZ81zKyZW9vj88++ww9evTA6dOnLSOoA4DgAZC8sA4Sj3pqhyUe9RnUEVXCoDV2X331FTZs2FDu+BNPPIGXXnoJM2bMqHLHiIisSYWjenrUmMu5q1sCmqFlVvbu3Yv79+/j2WefBQD0798fzz77rOUtowkeAEmZLdUkRtzzlshWGRTYZWVlISCg/PoOX19fZGZmVrlTRETWpNJ1eHrUmPNzD9apqb5lVkonSHh5eeH06dN45JFHAMDwoE7HKV+DKUceiUhnBgV2gYGBOHToEBo2bKh2/NChQ6hXr56Ws4iIbI+2RIes3AJEx51QJDroUWMuNEhRZsWYBYDPnz+PYcOGqRIkBg8ejNq1a+t8vkY6JoIQUfUyaI3d2LFjMWXKFKxevRqXL1/G5cuXsWrVKkydOhVjx441dh+JiCySTC4QG59e0co5xManQxYYpnONOWMWABZCYMWKFWjbti2OHz8Ob29vbN26FV9//XXVsl6ViSBlp5fzMhXH07cbfm0iqhKDRuymT5+OmzdvYsKECSgqKgIAODs7Y8aMGZg5c6ZRO0hEZE4VrZ3TOdHhci7C9KgxpywAXHZ611+PMislJSV48cUXsXXrVgBAjx49sG7dOtSvX1+ft19epYkgEkUiSLN+XA9HZAYGBXYSiQSLFi3Cu+++i7Nnz8LFxQWPPfYYnJycjN0/IiKzqWztnF77ybYeoChponH6snyNuaoWALa3t0dAQAAcHBwwf/58vPHGG5BKDZqkAfBfgCv7+wA665gIwvVxRNXPoDp2GzduRGRkpMbnpk+fjg8//LDKHbNWrGNHZBt0KRLs6eKIyBWHK73WxrGdENa4juKBCRMOioqKkJeXBx8fHwDAgwcP8Oeff6JVq1ZVum7pAHeANBGfOX5e+UmDVirKrRBRlZm8jl10dDR++eWXcsenTp2KuLg4Qy5JRGQxdF071y6otv77yepYY05f58+fR3h4OAYPHgyZTAYAcHFxMUpQFx13QjVqmQMv3U6sKGFELgMy/gBStyi+y2VV6iMR/cegwG79+vWIjIzEwYP/bej3+uuvY/Pmzdi7d6/ROkdEZA66rp07fvm20RIdDFU2QSI1NRUXLlwwyrU1BbjJ8ma4Ibwh1zrX818iiEbp24ElIcDaZ4EfRiu+LwlhwgWRkRgU2PXr1w9ffPEFBgwYgOPHj2PChAnYunUr9u7di2bNmhm7j0RE1UqftXPKRAd/T/W6cv6ezhXv6WoEN2/exKBBgzBu3Djk5+ejZ8+eSE1NRdOmTY1yfU0BrhxSxBZHKf67XHBXPhFEDbNpiUzOoOQJABg6dCju3LmDJ598Er6+vti/fz+aNGlizL5VSYMGDXD58mW1YwsWLKgwa7egoABvvPEGNm3ahMLCQvTp0wdffPEF6tbVsQYVEdkEXYv/KttVNdHBEHv27EFUVBSuX79utASJsrQFuL/KQxFdPAUxDutQD6W2NtOSCAKA2bRE1UTnwG7atGkaj/v6+qJt27b44osvVMc+/vjjqvfMCObOnatWV8/d3b3C9lOnTsXOnTvx/fffw9PTExMnTsT//vc/HDp0yNRdJSILEtpQ/yLByv1kq4NMJsOUKVNw/fp1PP7449i4cSPatm1r9NepKMD9VR6KXYXtESo9h7k9fPB44yYVJ4Losa0as2mJDKdzYHfy5EmNx5s0aYK8vDzV85a036C7uzv8/f11apubm4uVK1diw4YN6NGjBwBg9erVaN68OQ4fPoxOnTqZsqtEZEGURYKj405AAkACOUKl5+CHO8iBF47Km5l87VyF/bOzQ1xcHL788kt8+OGHVSs2XIHKAlwBKS67t0XjHj2Ayu6FHtuqEZHhDCp3Yg0aNGiAgoICFBcX49FHH8XQoUMxdepU2NtrjmX37NmDnj174vbt2/Dy8lIdDwoKwpQpUzB16lSN5xUWFqKwsFD1OC8vD4GBgSx3QmQDEtIyse+nVZhU/A3qSf6bcnzg4g+X/h9W29ZZQgh88803uHfvntbfRaaizIoFNJZV1n0dYcYfikSJyozYwRE7ojJMXu7EGkyaNAmbNm3C3r178eqrr+L999/HW2+9pbV9VlYWHB0d1YI6AKhbty6ysrK0nrdgwQJ4enqqvgIDA431FojIzCKkR7Gg5EMElArqAMDlQXa1LfYvnSDx1ltv4cyZMyZ/zdKMlhwSFK7ztmpEZDiDkyfMYebMmVi0aFGFbc6ePYtmzZqprQls2bIlHB0d8eqrr2LBggVG3SFj1qxZaq+lHLEjIiv3cLG/xIyL/Xfv3o2oqCjcuHFDlSDRvHlzk7xWRYySHCK1A/TYVo2IDGNVgd0bb7yBkSNHVtimUaNGGo937NgRJSUluHTpksZSAP7+/igqKsKdO3fURu2ys7MrXKfn5OTErdSIbJEZF/sXFRXhnXfeweLFiyGEQNOmTbFhwwaTJEjoyijJIcH6batGRPqzqsDO19cXvr6+Bp2bkpICqVQKPz8/jc+3a9cODg4O2L17NwYNGgRAUcn9ypUrCAsLM7jPRGSlzLTYXy6Xo3v37khMTAQAjBs3Dh9//LHJEiSqXfAAxSinibZVI6rp9ArsZs+ejeeeew7t2rUzVX+MIikpCUeOHEH37t3h7u6OpKQkTJ06FcOHD0ft2rUBANevX0fPnj2xbt06hIaGwtPTE6NHj8a0adPg7e0NDw8PvP766wgLC2NGLFFNVNGWWIa005FUKsULL7yAc+fO4ZtvvsHzzz9v1OsbzJh73Cq3VSMio9MrsLt27Rr69u0LR0dH9O/fHwMGDEDPnj3h6Ohoqv4ZxMnJCZs2bcKcOXNQWFiIhg0bYurUqWpr4YqLi3H+/Hnk5+erjn3yySeQSqUYNGiQWoFiIqqBlIv98zKhuaiuRPG8ERb737x5E9nZ2QgOVmxPNmnSJERGRmqdYah26du1TJ8u4vQpkYXRu9yJXC7HoUOHEB8fj23btiEzMxO9evXCc889h2effRbe3t6VX8SG6ZOSTEQWTrkFFgCNi/1fWFflwEaZIOHq6oqTJ0/Czc2tStczOtU9KPunwnj3gIgqZtJyJ1KpFF26dMEHH3yA8+fP48iRI+jYsSO++uor1KtXD0899RQWL16M69evG/wGiIgsgnKxv0eZkh4e9aoc0BQVFeGtt95Cr169cOPGDdjZ2VVYWsksKt0GDIrMYLmsOntFRBUwaoHif/75B9u3b8f27dvRpUsXvPnmm8a6tNXgiB2RDTLm+jIA586dw7Bhw3DihKLw76uvvoqPPvrI8hIkWFSYyCLoE1sYNSvW19cXo0ePxujRo415WSKqIplcVOsG9TbHSIv9hRBYsWIFpkyZggcPHsDb2xsrV67EwIEDq95HU+A2YERWx6rKnRCR/hLSMhEbn47M3ALVsQBPZ8T0D9Z91wAymp9++gkPHjxQZeXXq1fP3F3SzkyZwURkOJvdUoyI/tvns3RQBwBZuQWIjjuBhLRMM/WsZlGueJFIJFi9ejU+/fRT/Pbbb5Yd1AHcBozICjGwI7JRMrlAbHx6RcveERufDpncaMtsaza5TLEmLXWL4rtchsLCQkyfPh1jxoxRNatbty4mTZoEqdQKfv0qtwEDUD644zZgRJaIU7FENio541a5kbrSBIDM3AIkZ9yq+lZRNZ2GOm/nHtTB0HgJTp79GwAwYcIEiy/urhG3ASOyKkYJ7EaMGIGYmBit+7QSUfXLuas9qDOkHWlRps6bEAJfHy/G1F8z8KAEqOPljpVrvrXOoE6J24ARWQ2jBHaRkZGIiorC448/jnfffRcNGzY0xmWJqAr83J2N2o40KFPn7d98OcbGF+CncyUAgKcb2WPt8Lqo11+HkiGWjtuAEVkFoyzyiIiIwMGDBzF48GAMHz4cY8eOxZUrV4xxaSIyUGhDbwR4Ole07B0BnorSJ2Sgy4mq6UkhBHp/m4+fzpXAQQos7uWEX4e7oJ4kR9GOiKgaGHX1bp8+fbBy5UoAwOOPP27MSxORnuykEsT0V+w9qmXZO2L6B7OeXVWUqt8mkUjwXg8nNPeR4siYWngj3AlSiaRcOyIiUzLKVOyAAQNw/vx5FBcX47HHHkOzZs2wZMkSY1yaiKogIiQAy4e3LVfHzt/a69gZeScIQ53LLsDlCyXo00Txq/SZxxzQu7E97MsGy6zzRkTVpEpbik2ZMgWtWrVCSEgImjVrBnd3d2P2zSpxSzGyRDa184SGDFRFhuaiasvQLL2DhKMoxOnxtfCop6b7KVH0bUoqEw2IyGDVtqVYjx49cPr0afzyyy9IT08HADzxxBNo2bIlWrZsif79+1fl8kRkJHZSiW2UNCmTgaqSl6k4/sI6kwd3N2/exJgxY/DTTz8BAJ4MawUH6d9QTHCX7hfrvBFR9avSiF1ZBQUFSEtLw+nTp5GamopPPvnEWJe2GhyxIzIRuQxYEqI+UqfG9KNjv//+O0aMGIEbN27AwcEBCxYswNSpUyE9t0PDKGJ91nkjIqOothE7peLiYqxfvx7//PMPgoODMXLkSOuoqk5E1qNUBqpmAsi7rmhn5LIcQgjMmDEDH374IQCgWbNm2LBhA9q0aaNowDpvRGQhjBJ9vfTSSzh27BhcXFywY8cOtG3bFn/++acxLk1EpKBrZqkJMlAlEgkKChTJJ6+++iqOHz/+X1CnpKzz1mKw4juDOiIyA6OM2P3999/44YcfVI9TUlIwZswYHDhwwBiXJyLSPbPUSBmoQgjcu3dPlRT2wQcfoF+/fujTp49Rrk9EZApGGbFzd3fHhQsXVI9bt26N27dvG+PSREQKQeGKNXQVlVz2qK9oV0X//vsv/ve//6Ffv36QyWQAAGdnZwZ1RGTxjDJi9/nnn+O5557DM888g+DgYJw9exZBQUHGuDQRkYLUTlHSZHMUTJmB+vvvvyMqKgqZmZlwcHDA0aNH0alTpypdk4iouhhlxK5ly5Y4ceIE2rdvj8uXL6NJkybYvHmzMS5NRPSf4AGKkiYeZQore9RTL3UilwEZfwCpWxTf5bJKL11YWIjp06ejV69eyMzMRLNmzXDkyBEGdURkVYwyYhcZGYmvvvoKL774IrZt24aioiK4uroa49JEROoqy0A1oIDxuXPnMHToUJw8eRIAMH78eHz00Uf8PUZEVscodexatmyJ06dPIz09HS+88AK6du0KqVSKpUuXGqOPVoV17IjMSFsBY+VUrYYCxkIIhIeH4/Dhw6hTpw5WrlyJ5557rlq6S0SkC31iC6NMxTo4OEAIgdWrV2PmzJlYtmwZEhMTjXFpIiLdyGWKkbpyQR3+O5Yws9y0rEQiwYoVK9C/f3+kpqbaTFAnkwskXbyJbSnXkXTxJmRyo9WiJyILZpSp2OjoaLRt2xZ37tzBnDlzAAD37983xqWJiHSjRwHjXRcKcP78eUycOBEAEBISgu3bt1dPP6tBQlomYuPTkZlboDoW4OmMmP7BiAgJqOBMIrJ2RhmxGzNmDPbu3YvU1FTUqlULFy5c4IJjIqpeOhQmLiwReHPOh+jduzemTJmCY8eOVUPHqldCWiai406oBXUAkJVbgOi4E0hIyzRTz4ioOhhlxA4AvLy8VP/dpEkTrFmzxliXJiKqXCWFic/+I8PQrQ+QkhUPABg3bhyCg4Oro2fVRiYXiI1P1zoZLQEQG5+OXsH+sJNqqwdIRNZMrxG7H374Aa1bt1Y9njlzJlatWoXjx4+jsLDQ2H0jItKdlgLGQgh8eawI7b6+j5QsOXx8fLBt2zZ88cUXNpf1mpxxq9xIXWkCQGZuAZIzblVfp4ioWuk1Yrd69WqMHDlS9XjZsmWQyWQoKCiAnZ0dmjdvjgMHDqiN3hERVQsNBYyFEBi69QE2pZUAAHqFt8baLT8jIMA215nl3NUe1BnSjoisj14jdmfOnEHv3r3VjqWmpuLvv//G1q1b4eDggC+//NKoHSQi0lmZAsYSiQRPBtrD0U6Cj6a/goQ/jttsUAcAfu7ORm1HRNZHrxG7zMxMeHp6qh7b2dlBIpGgQYMGaNCgAe7fv4+lS5di5syZRu8oEZEuChv3wbX+zdDYPhu4l43XovzQp9gPjzVtZu6umVxoQ28EeDojK7dA4zo7CQB/T2eENvSu7q4RUTXRa8TOx8cHly5dUj3OyspS2xO2devWSE9PN1rniIj0cfbsWXTq1Am9I/oir04roMVgSBo9VSOCOgCwk0oQ01+REFI2NUL5OKZ/MBMniGyYXoFdjx49sHLlStVjZ2dn2Nn9t+G2VCpFcXGx8XpHRKQDIQS+/PJLtGvXDikpKcjNzcWff/5p7m6ZRURIAJYPbwt/T/XpVn9PZywf3pZ17IhsnF5TsdOnT0eHDh3QqlUrTJ48udzzhw4dQqNGjYzWOSKiyvz7778YM2YMtm3bBgDo1asX1q5da9Nr6SoTERKAXsH+SM64hZy7BfBzV0y/cqSOyPbpFdi1aNECcXFxGDp0KOLj4xEdHY0OHTrA3t4eBw8exKxZszB16lRT9ZWISM2uXbsQFRWFrKwsODo6YsGCBZgyZQqkUqPUXrdqdlIJwhrXMXc3iKia6V2gePDgwWjcuDGmTp2KIUOGQCJR/AtQCIH+/ftj2rRpRu8kEZEmS5cuRVZWFpo3b44NGzao1dkkIqqJJEIIg3eGvnLlCk6fPo179+7hiSeeQIsWLZCWloaQkBBj9tGq5OXlwdPTE7m5ufDw8DB3d4hsWk5ODhYvXow5c+bYXLFhIiIlfWKLKgV2Snfv3sXGjRuxcuVKHD9+HCUlJVW9pNViYEdkGsoEiVOnTrFeJhHVKPrEFlXaK/bAgQNYuXIlfvjhB7i6uqJLly42uak2ESn2ITXXYvx//vkHY8aMwfbt2wEoloQ8/fTT1fLaRETWRO8VxllZWVi4cCEee+wxPPPMMygpKcHmzZtx48YNxMbGmqKPetu3bx8kEonGr6NHj2o9r1u3buXajx8/vhp7TmSZEtIy0XnRHkSuOIzJm1IQueIwOi/ag4S0TJO/9q5du9CyZUts374djo6O+Pjjj9GjRw+Tvy4RkTXSa8Suf//+2L17N7p37445c+Zg4MCBqFWrlup5ZSKFuYWHhyMzU/0Pzrvvvovdu3ejffv2FZ47duxYzJ07V/WY63aopktIy0R03IlyOxlk5RYgOu6EyWqjFRYW4u2338bHH38MAEyQICLSgV6B3c6dOzF06FBMmTKl0gDJnBwdHeHv7696XFxcjG3btuH111+vNPh0dXVVO5eoJpPJBWLj0zVuTyWg2M0gNj4dvYL9jT4tO3DgQCQkJAAAJkyYgA8//JD/0CIiqoReU7GJiYlwcXFBjx490LRpU8ydOxcXL140Vd+MZvv27bh58yZGjRpVadv169fDx8cHISEhmDVrFvLz8ytsX1hYiLy8PLUvIluRnHELmbkFWp8XADJzC5Ccccvorz158mT4+fkhPj4ey5YtY1BHRKQDvQK7Tp06YcWKFcjMzMSMGTPw22+/4fHHH0enTp2wdOlSZGdnm6qfVbJy5Ur06dMHjzzySIXthg4diri4OOzduxezZs3Ct99+i+HDh1d4zoIFC+Dp6an6CgwMNGbXicwq5672oM6QdhX5559/sG/fPtXjiIgIXLx4Ec8++2yVr01EVFNUudzJ+fPnsXLlSnz77bfIzs6GRCKBTCYzVv/UzJw5E4sWLaqwzdmzZ9Gs2X8bfl+7dg1BQUHYvHkzBg0apNfr7dmzBz179sSFCxfQuHFjjW0KCwtRWFioepyXl4fAwECWOyGbkHTxJiJXHK603caxnaq0y8Fvv/2GESNGID8/H6dOnUKDBg0MvhYRka2ptnInANC0aVN88MEHWLBgAeLj47Fq1aqqXlKrN954AyNHjqywTdm9alevXo06depgwIABer9ex44dAaDCwM7JyQlOTk56X5vIGoQ29EaApzOycgs0rrOTQLG5fGhDb4OuX1hYiFmzZuGTTz4BAAQHB1e6/IGIiLSrcmCnZGdnh4EDB2LgwIHGumQ5vr6+8PX11bm9EAKrV69GVFQUHBwc9H69lJQUAKjRm4lTzWYnlSCmfzCi405AAqgFd8pUiZj+wQYlTqSnp2Po0KE4deoUAEWCxOLFi+Hi4lLlfhuVXAZcTgTuZQNudYGgcEBqZ+5eERFpZNM7Ze/ZswcZGRkYM2ZMueeuX7+OZs2aITk5GQBw8eJFzJs3D8ePH8elS5ewfft2REVF4amnnkLLli2ru+tEFiMiJADLh7eFv6ez2nF/T2eDS518+eWXaNeuHU6dOgUfHx9s374dy5Yts7ygLn07sCQEWPss8MNoxfclIYrjREQWyGgjdpZo5cqVCA8PV1tzp1RcXIzz58+rpn0cHR3x+++/Y8mSJbh//z4CAwMxaNAgvPPOO9XdbSKLExESgF7B/kbbeeL8+fMoKChA7969sWbNGsscFU/fDmyOAspOQudlKo6/sA4I1n+JBxGRKRllr1j6D/eKJdKsqKgIjo6OAICCggJs2rQJUVFRkEotcOJALlOMzOXd0NJAAnjUA6akclqWiExOn9jCAn+jEpEtKSwsxLRp09CzZ0+UlJQAAJydnTFy5EjLDOoAxZo6rUEdAAgg77qiHRGRBbHpqVgiMq+yCRK//fYbnnnmGTP3Sgf3dKzJqWs7IqJqYqH/XCYiayaEwPLly9USJOLj460jqAMU2a/GbEdEVE04YkdERvXPP/9g9OjRiI+PBwD06dMHa9assa49mIPCFWvo8jJRLnkCgGqNXVB4dfeMiKhCHLEjIqOKiopCfHw8HB0d8cknn+Dnn3+2rqAOUCRERCh3uSmb+fvwccRCJk4QkcVhYEdERvXRRx+hXbt2SE5OxpQpUyw3QaIywQMUJU08ypRi8ajHUidEZLFY7sTIWO6EapozZ84gKSlJrRC4EAISiWE17iwOd54gIjOr1r1iiWyFTC6MVoC3JhBC4IsvvsCbb76JoqIiBAcHIzxcsebMZoI6QBHENexi7l4QEemEgR0RgIS0TMTGpyMzt0B1LMDTGTH9gw3aMsvW/fPPP3jllVewY8cOAEBERAQaNWpk5l4REZGVLn4hMp6EtExEx51QC+oAICu3ANFxJ5CQlmmmnlmmX3/9FS1atMCOHTvg6OiIJUuWYOfOndaXIEFEZIMY2FGNJpMLxManayxooTwWG58OmZxLUQHg7bffRkREBLKzs/HEE0/g6NGjmDx5svUmSBAR2Rj+NqYaLTnjVrmRutIEgMzcAiRn3Kq+TlmwwMBAAMDEiRNx9OhRtGzZ0sw9IiKi0rjGjmq0nLvagzpD2lksAzM7hRDIyspCQIBineH48ePRpk0bdOrUydQ9JiIiAzCwoxrNz93ZqO0sUvp2IGGG+qb2HvUUBXgrqMWmTJBIS0tDSkoKPD09IZFIGNQREVkwTsVSjRba0BsBns7l9hZQkkCRHRva0Ls6u2U86duBzVHqQR2g2Cprc5TieQ1KJ0jcuHEDhw8frobOEhFRVTGwoxrNTipBTP9gAFo3jkJM/2DrrGcnlylG6ipKDUmYqWj3UEFBAaZOnapKkAgODsbRo0fRp0+faukyERFVDQM7qvEiQgKwfHhb+HuqT7f6ezpj+fC21lvH7nJi+ZE6NQLIu65oByA9PR0dO3bEkiVLACgSJI4dO8YECSIiK8I1dkRQBHe9gv1ta+eJe9l6tYuNjcXp06fh6+uL1atXo1+/fibsHBERmQIDO6KH7KQShDWuY+5uGI9bXb3aLVu2DE5OTvjggw8svtgwt38jItJMIoRg5VUj0mejXiKTksuAJSGKRAkN6+x+vVCChKvO+GRvjlVtas/t34ioptEntuAaOyJbJbVTlDQBUDo1pKBEYGpCASLW52PJgVv4cZvmzFhLxO3fiIgqxsCOyJYFDwBeWAd4KEayzuTI0PGb+1hypAiAIkEiIiLCnD3UGbd/IyKqHAM7IlsXPABiciqWSV5B+1VFOJ0th6+vL3bs2IGlS5fCxcXF3D3UCbd/IyKqHJMniGqAceOj8c033wAAIiIisHr1aotPkCirxmz/RkRUBRyxI6oBXnjhBTg7O2PJkiXYuXOn1QV1QA3Z/o2IqIo4YkdkgwoKCnD69GmEhoYCAHr16oVLly6hbl0dS6BYIOX2b1m5BRrX2UmgKCpttdu/EREZAUfsiGxMWloaQkND8fTTT+Pvv/9WHbfmoA6w8e3fiIiMhIEdkY0QQuDzzz9Hhw4dkJqaChcXF1y/ft3c3TIqm93+jYjISDgVS2QDcnJy8Morr2Dnzp0AgL59+2L16tVWP0qniU1u/0ZEZCQM7IisXEJCAkaOHIns7Gw4OTnhww8/xMSJEyGR2G6gY3PbvxERGQkDOyIr9/vvvyM7OxtPPPEENm7ciBYtWpi7S0REZCYM7IiskBBCNSI3f/58+Pj4YPLkyVZTbJiIiEyDyRNEVkSZINGzZ0+UlJQAAJycnDBz5kwGdURExBE7ImtRNkFi/fr1GDFihJl7ZXwyuWBiBBGRgRjYEVmBsgkSH3zwAaKioszdLaNLSMtEbHy62p6wAZ7OiOkfzFImREQ64FQskQUrKCjAlClT0LdvX1WCxNGjRzFp0iSby3pNSMtEdNwJtaAOALJyCxAddwIJaZlm6hkRkfVgYEdkwcaOHYtPP/0UAPD666/j6NGjNpn1KpMLxMana9wqTHksNj4dMrmmFkREpMTAjsiC/d///R8aNGiAnTt34rPPPrPZBInkjFvlRupKEwAycwuQnHGr+jpFRGSFrDKwmz9/PsLDw+Hq6govLy+Nba5cuYJ+/frB1dUVfn5+mD59uiqLUJtbt25h2LBh8PDwgJeXF0aPHo179+6Z4B0QaZaTk4ONGzeqHjdr1gx//fUXnnnmGTP2yvRy7moP6gxpR0RUU1llYFdUVIQhQ4YgOjpa4/MymQz9+vVDUVEREhMTsXbtWqxZswazZ8+u8LrDhg3DmTNnsGvXLuzYsQMHDhzAuHHjTPEWiMr55Zdf0KJFCwwfPhwHDx5UHbe3t/0cJz9358ob6dGOiKimssrALjY2FlOnTtW61ui3335Deno64uLi0Lp1a/Tt2xfz5s3DsmXLUFRUpPGcs2fPIiEhAd988w06duyIzp07Y+nSpdi0aRNu3LhhyrdDNVxBQQEmT56MZ555Bjk5OQgODtY6Em2rQht6I8DTGdrSQSRQZMeGNvSuzm4REVkdqwzsKpOUlIQWLVqobYDep08f5OXl4cyZM1rP8fLyQvv27VXHnn76aUilUhw5csTkfaaaKS0tDR06dMBnn30GAJg0aRKSk5MREhJi5p5VLzupBDH9gwGgXHCnfBzTP5j17IiIKmGTgV1WVpZaUAdA9TgrK0vrOX5+fmrH7O3t4e3trfUcACgsLEReXp7aF5EuvvrqK7Rv3x5paWnw8/PDzz//jE8//dRmEyQqExESgOXD28LfU3261d/TGcuHt2UdOyIiHVhMYDdz5kxIJJIKv86dO2fubpazYMECeHp6qr4CAwPN3SWyEhKJBIWFhejbty9Onz6Nvn37mrtLZhcREoCD07tiZ39gS+fr2NkfODi9K4M6IiIdWcyq7DfeeAMjR46ssE2jRo10upa/vz+Sk5PVjmVnZ6ue03ZOTk6O2rGSkhLcunVL6zkAMGvWLEybNk31OC8vj8EdaZWXlwcPDw8Aihp1AQEBePbZZ22u2LDB0rfDLmEGnsgrta71SD0gYhEQPMB8/SIishIWE9j5+vrC19fXKNcKCwvD/PnzkZOTo5pe3bVrFzw8PBAcHKz1nDt37uD48eNo164dAGDPnj2Qy+Xo2LGj1tdycnKCk5OTUfpNtqugoABvvfUWtm/fjq9/3IMHEif4uTvjmX4M6lTStwObo4CyZYrzMhXHX1jH4I6IqBIWE9jp48qVK7h16xauXLkCmUyGlJQUAECTJk3g5uaG3r17Izg4GC+//DI++OADZGVl4Z133sFrr72mCsKSk5MRFRWF3bt3o379+mjevDkiIiIwduxYfPnllyguLsbEiRPx0ksvoV69emZ8t1Rt5DLgciJwLxtwqwsEhQNSuypfNi0tDZGRkUhLSwMADH13GdxCegDgPqgqchmQMAPlgjrg4TEJkDATaNbPKD8TIiJbZZWB3ezZs7F27VrV4zZt2gAA9u7di27dusHOzg47duxAdHQ0wsLCUKtWLYwYMQJz585VnZOfn4/z58+juLhYdWz9+vWYOHEievbsCalUikGDBqmyFcnGpW9XBBalpwA9qjYFKITA559/junTp6OwsBBSVy/4PDMFLo3/y7xW7oNa45MDLieq3/tyBJB3XdGuYZdq6xYRkbWRCCG4+aIR5eXlwdPTE7m5uaq1VGThtE0BKgttGDAFmJ2djVGjRuGXX34BAHg1DYVbr9dhV6t2ubYSKDI/D87oUXPLeaRuAX4YXXm7QSuBFoNN3x8iIguiT2xhMVmxRGZR6RQgFFOAcplel3377bfxyy+/wMnJCdNmL4THc+/CrlZtSCFHJ2k6BkgT0UmaDink3AcVUEx9G7MdEVENZZVTsURGY6IpwA8++ADXr1/H4sWLcbGkNn7YlII+0mTEOKxDPcl/AdwN4Y3Y4ij8Kg+t2fugBoUrpr7zMqE5yJYong8Kr+6eERFZFY7YUc12L9so7dLS0vB///d/UK5sqFOnDhISEhASEgI/d2f0kSZjucMS+EN9VM4ft7DcYQn6SJNr9j6oUjvFekYAWveeiFjIxAkiokowsKOarYpTgEIILF26FO3bt8f777+P9evXl2sTGuSJuY7fAgDKLqFTPo51/BahQZ46d9smBQ9QrGf0KJNE4lGPpU6IiHTEqViq2aowBZidnY1XXnkFP//8MwDgmWeeQa9evcq1s7uahLq4WX4g6iGpBPDHTeBqEjM+gwcoSpqYoOwMEVFNwMCOajblFODmKCgir9LBnfYpwJ9//hmjRo1CTk4OnJycsHjxYrz22muaiw0babq3xpDaMcAlIjIQp2KJ9JwCfO+999CvXz/k5OQgJCQEx44dw8SJE7XvIMGMTyIiqiYcsSMC9JoCDA8Ph1QqxcSJE7Fo0SI4O1eS9MCMTyIiqiYM7IiUtEwBCiFw/vx5NGvWDADQo0cPnD17Fo8//rju1zVgupeIiEhfnIolqkB2djb69euHDh064OLFi6rjOgd1Ssz4JCKiasAROyItdu7ciVGjRuGff/6Bk5MTUlJS0LhxY8MvyIxPIiIyMQZ2RGU8ePAAb731Fj7//HMAQIsWLbBx40Y88cQTVb84Mz6JiMiEOBVLVEpqaio6dOigCuomT56M5ORk4wR1REREJsYRO6JSNmzYgDNnzqBu3bpYs2YNIiIizN0lIiIinTGwIyolNjYWxcXFeOutt+Dn52fu7hAREelFIpS7lpNR5OXlwdPTE7m5ufDw8DB3d4iIiMjK6RNbcI0dERERkY1gYEdERERkIxjYEREREdkIBnZERERENoKBHREREZGNYGBHREREZCMY2BERERHZCAZ2RERERDaCgR0RERGRjWBgR0RERGQjGNgRERER2QgGdkREREQ2goEdERERkY1gYEdERERkIxjYEREREdkIBnZERERENoKBHREREZGNYGBHREREZCMY2BERERHZCAZ2RERERDaCgR0RERGRjWBgR0RERGQjGNgRERER2QirDOzmz5+P8PBwuLq6wsvLq9zzp06dQmRkJAIDA+Hi4oLmzZvj008/rfS6DRo0gEQiUftauHChCd4BERERkfHZm7sDhigqKsKQIUMQFhaGlStXlnv++PHj8PPzQ1xcHAIDA5GYmIhx48bBzs4OEydOrPDac+fOxdixY1WP3d3djd5/IiIic5LJZCguLjZ3N+ghBwcH2NnZGeVaVhnYxcbGAgDWrFmj8flXXnlF7XGjRo2QlJSErVu3VhrYubu7w9/f3yj9JCIisiRCCGRlZeHOnTvm7gqV4eXlBX9/f0gkkipdxyoDO0Pk5ubC29u70nYLFy7EvHnz8Oijj2Lo0KGYOnUq7O1rzG0iIiIbpgzq/Pz84OrqWuUggqpOCIH8/Hzk5OQAAAICAqp0vRoRsSQmJuK7777Dzp07K2w3adIktG3bFt7e3khMTMSsWbOQmZmJjz/+WOs5hYWFKCwsVD3Oy8szWr+JiIiMRSaTqYK6OnXqmLs7VIqLiwsAICcnB35+flWalrWY5ImZM2eWS1wo+3Xu3Dm9r5uWlobnnnsOMTEx6N27d4Vtp02bhm7duqFly5YYP348PvroIyxdulQtcCtrwYIF8PT0VH0FBgbq3UciIiJTU66pc3V1NXNPSBPlz6Wqax8tZsTujTfewMiRIyts06hRI72umZ6ejp49e2LcuHF455139O5Tx44dUVJSgkuXLqFp06Ya28yaNQvTpk1TPc7Ly2NwR0REFovTr5bJWD8XiwnsfH194evra7TrnTlzBj169MCIESMwf/58g66RkpICqVQKPz8/rW2cnJzg5ORkaDeJiIiIjMZipmL1ceXKFaSkpODKlSuQyWRISUlBSkoK7t27B0Ax/dq9e3f07t0b06ZNQ1ZWFrKysvDPP/+orpGcnIxmzZrh+vXrAICkpCQsWbIEp06dwt9//43169dj6tSpGD58OGrXrm2W90lERETA8uXL0bJlS3h4eMDDwwNhYWH45ZdfVM+XrkPr4uKCBg0a4IUXXsCePXvUrnPp0iW1JV7e3t7o2rUr/vjjD7V2c+bMQevWrVWP8/PzMWvWLDRu3BjOzs7w9fVF165dsW3bNlWbbt26aa1/269fP0gkEsyZM8c4N6QCVhnYzZ49G23atEFMTAzu3buHNm3aoE2bNjh27BgAYMuWLfjnn38QFxeHgIAA1VeHDh1U18jPz8f58+dVc9lOTk7YtGkTunbtiieeeALz58/H1KlT8fXXX5vlPRIREZHCI488goULF+L48eM4duwYevTogeeeew5nzpxRtZk7dy4yMzNx/vx5rFu3Dl5eXnj66ac1ztr9/vvvyMzMxIEDB1CvXj08++yzyM7O1vr648ePx9atW7F06VKcO3cOCQkJGDx4MG7evKnWLjAwsFwptuvXr2P37t1VznbVmSCjys3NFQBEbm6uubtCRESk8uDBA5Geni4ePHhQ5WuVyOQi8cK/4qeT10TihX9FiUxuhB7qp3bt2uKbb74RQggRFBQkPvnkk3JtZs+eLaRSqTh37pwQQoiMjAwBQJw8eVLV5vTp0wKA2LZtm+pYTEyMaNWqleqxp6enWLNmTYX96dq1q4iOjhZ16tQRBw8eVB2fP3++6N+/v2jVqpWIiYnRen5FPx99YgurHLEjIiIi80hIy0TnRXsQueIwJm9KQeSKw+i8aA8S0jKr5fVlMhk2bdqE+/fvIywsrMK2kydPhhBCbcq0tAcPHmDdunUAAEdHR63X8ff3x88//4y7d+9W+HqOjo4YNmwYVq9erTq2Zs2achsnmBIDOyIiItJJQlomouNOIDO3QO14Vm4BouNOmDS4S01NhZubG5ycnDB+/Hj8+OOPCA4OrvAcb29v+Pn54dKlS2rHw8PD4ebmhlq1amHx4sVo164devbsqfU6X3/9NRITE1GnTh106NABU6dOxaFDhzS2feWVV7B582bcv38fBw4cQG5uLp599lm936+hGNgRERFRpWRygdj4dAgNzymPxcanQybX1KLqmjZtipSUFBw5cgTR0dEYMWIE0tPTKz1PCFGulMh3332HkydP4ocffkCTJk2wZs0aODg4aL3GU089hb///hu7d+/G4MGDcebMGXTp0gXz5s0r17ZVq1Z47LHHsGXLFqxatQovv/xyte5gZTHlToiIiMhyJWfcKjdSV5oAkJlbgOSMWwhrbPydLRwdHdGkSRMAQLt27XD06FF8+umn+Oqrr7Sec/PmTfzzzz9o2LCh2vHAwEA89thjeOyxx1BSUoLnn38eaWlpFZYvc3BwQJcuXdClSxfMmDED7733HubOnYsZM2aUm8Z95ZVXsGzZMqSnpyM5ObkK71p/HLEjIiKiSuXc1R7UGdKuquRyeYU7QwHAp59+CqlUioEDB2ptM3jwYNjb2+OLL77Q6/WDg4NRUlKCgoLy73fo0KFITU1FSEhIpdPFxsYROyIiIqqUn7uzUdvpY9asWejbty8effRR3L17Fxs2bMC+ffvw66+/qtrcvXsXWVlZKC4uRkZGBuLi4vDNN99gwYIFqpE+TSQSCSZNmoQ5c+bg1Vdf1bjlWrdu3RAZGYn27dujTp06SE9Px9tvv43u3bvDw8OjXPvatWsjMzOzwuldU+GIHREREVUqtKE3AjydoW3jKwmAAE9nhDb0Nvpr5+TkICoqCk2bNkXPnj1x9OhR/Prrr+jVq5eqzezZsxEQEIAmTZrg5ZdfRm5uLnbv3o0ZM2ZUev0RI0aguLgYn3/+ucbn+/Tpg7Vr16J3795o3rw5Xn/9dfTp0webN2/Wek0vLy/UqlVL/zdbRRIhhGlWOdZQeXl58PT0RG5ursYonoiIyBwKCgqQkZGBhg0bwtnZsFE1ZVYsALUkCmWwt3x4W0SEVFMhXhtT0c9Hn9iCI3ZERESkk4iQACwf3hb+nuqBh7+nM4M6C8E1dkRERKSziJAA9Ar2R3LGLeTcLYCfu2L61U6qbZKWqhMDOyIiItKLnVRikpImVHWciiUiIiKyEQzsiIiIiGwEAzsiIiIiG8HAjoiIiMhGMLAjIiIishEM7IiIiIhsBAM7IiIiIhvBwI6IdCOXARl/AKlbFN/lMnP3iIhqiJEjR0IikZT7unDhgtbnIiIiVOc3aNBAdbxWrVpo27Ytvv/+ezO+I9NhgWIiqlz6diBhBpB3479jHvWAiEVA8ADz9YuIaoyIiAisXr1a7Zivr6/W55ycnNQez507F2PHjkVeXh4++ugjvPjii6hfvz7Cw8NN2/FqxsCOiCqWvh3YHAX1Lb8B5GUqjr+wjsEdUU0jlwGXE4F72YBbXSAoHJDamfQlnZyc4O/vr/dzSu7u7vD394e/vz+WLVuGuLg4xMfHM7AjohpELlOM1JUN6oCHxyRAwkygWT+T/1InIgthAyP49vb2cHBwQFFRkbm7YnRcY0dE2l1OVP/lXY4A8q4r2hGR7VOO4Jf9vaAcwU/fbrKX3rFjB9zc3FRfQ4YM0fqcm5sb3n//fY3XKSoqwoIFC5Cbm4sePXqYrL/mwhE7ItLuXrZx2xGR9TLzCH737t2xfPly1eNatWppfQ4AvL291R7PmDED77zzDgoKCuDm5oaFCxeiX79+Ru+nuTGwIyLt3Ooatx0RWS99RvAbdjH6y9eqVQtNmjTR+zml6dOnY+TIkXBzc0PdunUhkUiM3kdLwMCOiLQLClesncnLhOZ/pUsUzwfZ1uJjItLAykfwfXx8Kg3+bAEDOyLSTmqnWBC9OQqABOrB3cN/7UYsZOIEUU1gwSP4hYWFyMrKUjtmb28PHx+fau+LuTF5gogqFjxAUdLEI0D9uEc9ljohqkmUI/jQNoUpATzqm2UEPyEhAQEBAWpfnTt3rvZ+WAKJEELT/AoZKC8vD56ensjNzYWHh4e5u0NkPGaoW0VExlNQUICMjAw0bNgQzs7Ohl1EVdcS0DiCz3/sGayin48+sQWnYolIN1I7kyyIJiIrohzB11jHbiGDOgvAwI6IiIh0FzxAUdKEI/gWiYEdERER6Ycj+BaLyRNERERENoKBHREREZGNYGBHRERUg7AYhmUy1s+FgR0REVEN4ODgAADIz883c09IE+XPRflzMhSTJ4iIiGoAOzs7eHl5IScnBwDg6upqs/ulWhMhBPLz85GTkwMvLy/Y2VUtu5iBHRERUQ3h7+8PAKrgjiyHl5eX6udTFQzsiIiIagiJRIKAgAD4+fmhuLjY3N2hhxwcHKo8UqdklYHd/PnzsXPnTqSkpMDR0RF37twp10bT8PLGjRvx0ksvab3urVu38PrrryM+Ph5SqRSDBg3Cp59+Cjc3N2N2n4iIyKzs7OyMFkiQZbHK5ImioiIMGTIE0dHRFbZbvXo1MjMzVV8DBw6ssP2wYcNw5swZ7Nq1Czt27MCBAwcwbtw4I/aciIiIyHSscsQuNjYWALBmzZoK2+kzX3327FkkJCTg6NGjaN++PQBg6dKleOaZZ7B48WLUq1evSn0mIiIiMjWrHLHT1WuvvQYfHx+EhoZi1apVFdaISUpKgpeXlyqoA4Cnn34aUqkUR44cqY7uEhEREVWJVY7Y6WLu3Lno0aMHXF1d8dtvv2HChAm4d+8eJk2apLF9VlYW/Pz81I7Z29vD29sbWVlZWl+nsLAQhYWFqse5ubkAgLy8PCO8CyIiIqrplDGFLkWMLSawmzlzJhYtWlRhm7Nnz6JZs2Y6Xe/dd99V/XebNm1w//59fPjhh1oDO0MtWLBANTVcWmBgoFFfh4iIiGq2u3fvwtPTs8I2FhPYvfHGGxg5cmSFbRo1amTw9Tt27Ih58+ahsLAQTk5O5Z739/cvV9enpKQEt27dqnCd3qxZszBt2jTV4zt37iAoKAhXrlyp9ObXJHl5eQgMDMTVq1fh4eFh7u5YDN4XzXhfyuM90Yz3RTPeF82s9b4IIXD37l2d1vtbTGDn6+sLX19fk10/JSUFtWvX1hjUAUBYWBju3LmD48ePo127dgCAPXv2QC6Xo2PHjlqv6+TkpPGanp6eVvWhqS4eHh68LxrwvmjG+1Ie74lmvC+a8b5oZo33RdfBIosJ7PRx5coV3Lp1C1euXIFMJkNKSgoAoEmTJnBzc0N8fDyys7PRqVMnODs7Y9euXXj//ffx5ptvqq6RnJyMqKgo7N69G/Xr10fz5s0RERGBsWPH4ssvv0RxcTEmTpyIl156iRmxREREZBWsMrCbPXs21q5dq3rcpk0bAMDevXvRrVs3ODg4YNmyZZg6dSqEEGjSpAk+/vhjjB07VnVOfn4+zp8/r1Z5e/369Zg4cSJ69uypKlD82WefVd8bIyIiIqoCqwzs1qxZU2ENu4iICERERFR4jW7dupXLLvH29saGDRuq1DcnJyfExMRonfKtqXhfNON90Yz3pTzeE814XzTjfdGsJtwXidAld5aIiIiILJ5NFygmIiIiqkkY2BERERHZCAZ2RERERDaCgZ2e5s+fj/DwcLi6usLLy0tjG4lEUu5r06ZNFV731q1bGDZsGDw8PODl5YXRo0fj3r17JngHplHZfTl16hQiIyMRGBgIFxcXNG/eHJ9++mml123QoEG5e7lw4UITvAPT0OXzcuXKFfTr1w+urq7w8/PD9OnTUVJSUuF1rf3zUtq+ffs0/j8jkUhw9OhRred169atXPvx48dXY89Nz5DPf0FBAV577TXUqVMHbm5uGDRoELKzs6upx6Z36dIljB49Gg0bNoSLiwsaN26MmJgYFBUVVXieLX5eli1bhgYNGsDZ2RkdO3ZEcnJyhe2///57NGvWDM7OzmjRogV+/vnnaupp9ViwYAE6dOgAd3d3+Pn5YeDAgTh//nyF56xZs6bc58LZ2bmaemwaVpkVa05FRUUYMmQIwsLCsHLlSq3tVq9erZaZq+2PutKwYcOQmZmJXbt2obi4GKNGjcK4ceOqnKVbXSq7L8ePH4efnx/i4uIQGBiIxMREjBs3DnZ2dpg4cWKF1547d65aqRp3d3ej999UKrsvMpkM/fr1g7+/PxITE5GZmYmoqCg4ODjg/fff13pda/+8lBYeHo7MzEy1Y++++y52796N9u3bV3ju2LFjMXfuXNVjV1dXk/TRnPT9/E+dOhU7d+7E999/D09PT0ycOBH/+9//cOjQIVN3tVqcO3cOcrkcX331FZo0aYK0tDSMHTsW9+/fx+LFiys815Y+L9999x2mTZuGL7/8Eh07dsSSJUvQp08fnD9/vty+5wCQmJiIyMhILFiwAM8++yw2bNiAgQMH4sSJEwgJCTHDOzC+/fv347XXXkOHDh1QUlKCt99+G71790Z6ejpq1aql9TwPDw+1AFAikVRHd01HkEFWr14tPD09NT4HQPz44486Xys9PV0AEEePHlUd++WXX4REIhHXr1+vYk+rV0X3pawJEyaI7t27V9gmKChIfPLJJ1XvmJlpuy8///yzkEqlIisrS3Vs+fLlwsPDQxQWFmq8li19XjQpKioSvr6+Yu7cuRW269q1q5g8eXL1dMpM9P3837lzRzg4OIjvv/9edezs2bMCgEhKSjJBDy3DBx98IBo2bFhhG1v7vISGhorXXntN9Vgmk4l69eqJBQsWaGz/wgsviH79+qkd69ixo3j11VdN2k9zysnJEQDE/v37tbbR52+WteBUrIm89tpr8PHxQWhoKFatWlWuZl5pSUlJ8PLyUhudePrppyGVSnHkyJHq6K5Z5Obmwtvbu9J2CxcuRJ06ddCmTRt8+OGHlU5TWpOkpCS0aNECdevWVR3r06cP8vLycObMGa3n2PLnZfv27bh58yZGjRpVadv169fDx8cHISEhmDVrFvLz86uhh9VLn8//8ePHUVxcjKefflp1rFmzZnj00UeRlJRUHd01C11/l9jK56WoqAjHjx9X+zlLpVI8/fTTWn/OSUlJau0Bxe8aW/9cAKj0s3Hv3j0EBQUhMDAQzz33nNbfvdaCU7EmMHfuXPTo0QOurq747bffMGHCBNy7dw+TJk3S2D4rK6vc0Lm9vT28vb2RlZVVHV2udomJifjuu++wc+fOCttNmjQJbdu2hbe3NxITEzFr1ixkZmbi448/rqaemlZWVpZaUAdA9Vjbz97WPy8rV65Enz598Mgjj1TYbujQoQgKCkK9evVw+vRpzJgxA+fPn8fWrVurqaemp+/nPysrC46OjuWWftStW9cmPhuaXLhwAUuXLq10GtaWPi///vsvZDKZxt8d586d03iOtt81tvq5kMvlmDJlCp588skKp5qbNm2KVatWoWXLlsjNzcXixYsRHh6OM2fOVPo7yGKZe8jQEsyYMUMAqPDr7NmzaufoM3z77rvvikceeUTr8/PnzxePP/54ueO+vr7iiy++0Ou9GJOp7ktqaqrw8fER8+bN07tPK1euFPb29qKgoEDvc43FmPdl7Nixonfv3mrH7t+/LwCIn3/+WePrW+rnpSxD7tPVq1eFVCoVW7Zs0fv1du/eLQCICxcuGOstmIQh90Wpss//+vXrhaOjY7njHTp0EG+99ZZR34exGXJfrl27Jho3bixGjx6t9+tZy+dFk+vXrwsAIjExUe349OnTRWhoqMZzHBwcxIYNG9SOLVu2TPj5+Zmsn+Y0fvx4ERQUJK5evarXeUVFRaJx48binXfeMVHPTI8jdgDeeOMNjBw5ssI2jRo1Mvj6HTt2xLx581BYWKhxGxN/f3/k5OSoHSspKcGtW7fg7+9v8OtWlSnuS3p6Onr27Ilx48bhnXfe0btPHTt2RElJCS5duoSmTZvqfb4xGPO++Pv7l8tkU2YwavvZW+rnpSxD7tPq1atRp04dDBgwQO/X69ixIwDFCE7jxo31Pr+6VOXzU9nn39/fH0VFRbhz547aqF12drZFfTY00fe+3LhxA927d0d4eDi+/vprvV/PWj4vmvj4+MDOzq5ctnNFP2d/f3+92luziRMnYseOHThw4IDeo24ODg5o06YNLly4YKLemR4DOwC+vr7w9fU12fVTUlJQu3ZtrXvThYWF4c6dOzh+/DjatWsHANizZw/kcrnql485GPu+nDlzBj169MCIESMwf/58g66RkpICqVSqMeuruhjzvoSFhWH+/PnIyclRvaddu3bBw8MDwcHBWs+xxM9LWfreJyEEVq9ercoK1ldKSgoAICAgQO9zq1NVPj+Vff7btWsHBwcH7N69G4MGDQIAnD9/HleuXEFYWJjBfa4O+tyX69evo3v37mjXrh1Wr14NqVT/5eLW8nnRxNHREe3atcPu3bsxcOBAAIqpx927d2utMhAWFobdu3djypQpqmO7du2y+M+FPoQQeP311/Hjjz9i3759aNiwod7XkMlkSE1NxTPPPGOCHlYTcw8ZWpvLly+LkydPitjYWOHm5iZOnjwpTp48Ke7evSuEEGL79u1ixYoVIjU1Vfz111/iiy++EK6urmL27Nmqaxw5ckQ0bdpUXLt2TXUsIiJCtGnTRhw5ckQcPHhQPPbYYyIyMrLa35+hKrsvqampwtfXVwwfPlxkZmaqvnJyclTXKHtfEhMTxSeffCJSUlLExYsXRVxcnPD19RVRUVFmeY+GqOy+lJSUiJCQENG7d2+RkpIiEhIShK+vr5g1a5bqGrb4edHk999/1zoNee3aNdG0aVNx5MgRIYQQFy5cEHPnzhXHjh0TGRkZYtu2baJRo0biqaeequ5um4wun/+y90UIxRTUo48+Kvbs2SOOHTsmwsLCRFhYmDnegklcu3ZNNGnSRPTs2VNcu3ZN7fdJ6Ta2/nnZtGmTcHJyEmvWrBHp6eli3LhxwsvLS5Vh//LLL4uZM2eq2h86dEjY29uLxYsXi7Nnz4qYmBjh4OAgUlNTzfUWjC46Olp4enqKffv2qX0u8vPzVW3K3pfY2Fjx66+/iosXL4rjx4+Ll156STg7O4szZ86Y4y0YBQM7PY0YMULj2o+9e/cKIRRlJ1q3bi3c3NxErVq1RKtWrcSXX34pZDKZ6hp79+4VAERGRobq2M2bN0VkZKRwc3MTHh4eYtSoUao//tagsvsSExOj8fmgoCDVNcrel+PHj4uOHTsKT09P4ezsLJo3by7ef/99s66v01dl90UIIS5duiT69u0rXFxchI+Pj3jjjTdEcXGx6nlb/LxoEhkZKcLDwzU+l5GRoXbfrly5Ip566inh7e0tnJycRJMmTcT06dNFbm5uNfbYtHT5/Je9L0II8eDBAzFhwgRRu3Zt4erqKp5//nm1oMfarV69WusaPKWa8nlZunSpePTRR4Wjo6MIDQ0Vhw8fVj3XtWtXMWLECLX2mzdvFo8//rhwdHQUTzzxhNi5c2c199i0tH0uVq9erWpT9r5MmTJFdQ/r1q0rnnnmGXHixInq77wRSYSooA4HEREREVkN1rEjIiIishEM7IiIiIhsBAM7IiIiIhvBwI6IiIjIRjCwIyIiIrIRDOyIiIiIbAQDOyIiIiIbwcCOiIiIyEYwsCOiGuXSpUuQSCSqvUJtxciRI1X7hprCvn37IJFIcOfOHZO9hjbdunVT2+OUiLRjYEdENUpgYCAyMzMREhJi7q5gzpw5aN26tc7tY2NjMXz4cNN1yAD79+9HYGCgubtBRA8xsCOiGqOoqAh2dnbw9/eHvb29ubujt23btmHAgAHm7oaabdu2oX///ubuBhE9xMCOiIxOLpdjwYIFaNiwIVxcXNCqVSts2bIFACCEwNNPP40+ffpAuVX1rVu38Mgjj2D27NkA/pv227lzJ1q2bAlnZ2d06tQJaWlpaq9z8OBBdOnSBS4uLggMDMSkSZNw//591fMNGjTAvHnzEBUVBQ8PD4wbN67cVKzytX799Ve0adMGLi4u6NGjB3JycvDLL7+gefPm8PDwwNChQ5Gfn6/Teyx93d27d6N9+/ZwdXVFeHg4zp8/DwBYs2YNYmNjcerUKUgkEkgkEqxZs0brPb169SrOnDmDiIgInX4GR48eha+vLxYtWqQ6Fh8fjw4dOsDZ2Rk+Pj54/vnnVc99++23aN++Pdzd3eHv74+hQ4ciJyen0tfZvn27Ktjs1q0bXn/9dUyZMgW1a9dG3bp1sWLFCty/fx+jRo2Cu7s7mjRpgl9++UXtGvv370doaCicnJwQEBCAmTNnoqSkRKf3SURlCCIiI3vvvfdEs2bNREJCgrh48aJYvXq1cHJyEvv27RNCCHHt2jVRu3ZtsWTJEiGEEEOGDBGhoaGiuLhYCCHE3r17BQDRvHlz8dtvv4nTp0+LZ599VjRo0EAUFRUJIYS4cOGCqFWrlvjkk0/En3/+KQ4dOiTatGkjRo4cqepHUFCQ8PDwEIsXLxYXLlwQFy5cEBkZGQKAOHnypNprderUSRw8eFCcOHFCNGnSRHTt2lX07t1bnDhxQhw4cEDUqVNHLFy4UOf3qLxux44dxb59+8SZM2dEly5dRHh4uBBCiPz8fPHGG2+IJ554QmRmZorMzEyRn5+v9Z5+/vnnonfv3lqfHzFihHjuueeEEELs3r1beHp6iq+++kr1/I4dO4SdnZ2YPXu2SE9PFykpKeL9999XPb9y5Urx888/i4sXL4qkpCQRFhYm+vbtq3pe+X5u376tOpaWlibc3d1FYWGhEEKIrl27Cnd3dzFv3jzx559/innz5gk7OzvRt29f8fXXX4s///xTREdHizp16oj79++rPguurq5iwoQJ4uzZs+LHH38UPj4+IiYmRvU6Xbt2FZMnT9b63onoPwzsiMioCgoKhKurq0hMTFQ7Pnr0aBEZGal6vHnzZuHs7CxmzpwpatWqJf7880/Vc8ogYtOmTapjN2/eFC4uLuK7775TXW/cuHFqr/HHH38IqVQqHjx4IIRQBHYDBw5Ua6MtsPv9999VbRYsWCAAiIsXL6qOvfrqq6JPnz46v0dN1925c6cAoOpfTEyMaNWqlbZbqaZXr17i888/1/q8MrDbunWrcHNzU7t3QggRFhYmhg0bptNrCSHE0aNHBQBx9+5dtfdTOrCbP3++GDx4sOpx165dRefOnVWPS0pKRK1atcTLL7+sOpaZmSkAiKSkJCGEEG+//bZo2rSpkMvlqjbLli0Tbm5uQiaTqa7LwI5IN9a3yISILNqFCxeQn5+PXr16qR0vKipCmzZtVI+HDBmCH3/8EQsXLsTy5cvx2GOPlbtWWFiY6r+9vb3RtGlTnD17FgBw6tQpnD59GuvXr1e1EUJALpcjIyMDzZs3BwC0b99ep363bNlS9d9169aFq6srGjVqpHYsOTlZr/dY9roBAQEAgJycHDz66KM69QsA8vLysH//fqxcubLCdkeOHMGOHTuwZcuWchmyKSkpGDt2rNZzjx8/jjlz5uDUqVO4ffs25HI5AODKlSsIDg7WeM62bdswceJEtWOl36+dnR3q1KmDFi1aqI7VrVsXAFTTvGfPnkVYWBgkEomqzZNPPol79+7h2rVret0nIgIY2BGRUd27dw8AsHPnTtSvX1/tOScnJ9V/5+fn4/jx47Czs8Nff/1l0Ou8+uqrmDRpUrnnSgcDtWrV0ul6Dg4Oqv+WSCRqj5XHlMGOru9R03UBqK6jq19++QXBwcGVZp82btwYderUwapVq9CvXz+113ZxcdF63v3799GnTx/06dMH69evh6+vL65cuYI+ffqgqKhI4zmZmZk4efIk+vXrp3Zc030zxj0gIt0wsCMiowoODoaTkxOuXLmCrl27am33xhtvQCqV4pdffsEzzzyDfv36oUePHmptDh8+rArSbt++jT///FM1Ete2bVukp6ejSZMmpnszWuj6Hivj6OgImUxWabtt27bhueeeq7Sdj48Ptm7dim7duuGFF17A5s2bVUFVy5YtsXv3bowaNarceefOncPNmzexcOFCVfB47NixCl8rPj4e4eHh8Pb2rrRfFWnevDl++OEHCCFUQd+hQ4fg7u6ORx55pErXJqqJmBVLREbl7u6ON998E1OnTsXatWtx8eJFnDhxAkuXLsXatWsBKEa6Vq1ahfXr16NXr16YPn06RowYgdu3b6tda+7cudi9ezfS0tIwcuRI+Pj4qKYYZ8yYgcTEREycOBEpKSn466+/NE4Nmus96qJBgwbIyMhASkoK/v33XxQWFpZrU1JSgl9++UXnMid+fn7Ys2cPzp07h8jISFV2aUxMDDZu3IiYmBicPXsWqampqozZRx99FI6Ojli6dCn+/vtvbN++HfPmzavwdUpnw1bFhAkTcPXqVbz++us4d+4ctm3bhpiYGEybNg1SKf9EEemL/9cQkdHNmzcP7777LhYsWIDmzZsjIiICO3fuRMOGDfHPP/9g9OjRmDNnDtq2bQtAUXi3bt26GD9+vNp1Fi5ciMmTJ6Ndu3bIyspCfHw8HB0dAShGoPbv348///wTXbp0QZs2bTB79mzUq1fP7O9RV4MGDUJERAS6d+8OX19fbNy4sVyb/fv3w83NTXWvdOHv7489e/YgNTUVw4YNg0wmQ7du3fD9999j+/btaN26NXr06KFaM+jr64s1a9bg+++/R3BwMBYuXIjFixdrvf79+/exe/duowR29evXx88//4zk5GS0atUK48ePx+jRo/HOO+9U+dpENZFEiIeFpIiILMS+ffvQvXt33L59G15eXubujllNmjQJJSUl+OKLL8zdFZWtW7finXfeQXp6urm7QkRlcI0dEZEFCwkJUcsOtgRubm5qhY+JyHJwxI6ILA5H7IiIDMPAjoiIiMhGMHmCiIiIyEYwsCMiIiKyEQzsiIiIiGwEAzsiIiIiG8HAjoiIiMhGMLAjIiIishEM7IiIiIhsBAM7IiIiIhvBwI6IiIjIRvw/t1Xy8fw8I8oAAAAASUVORK5CYII=\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["* feel free to use this notebook how you desire\n", "* run the whole dataset or upload your own!\n", "* we've included a section below where you can make your own molecule and run calculations\n", "* get creative!"], "metadata": {"id": "TwszZ0MfhZ6w"}}, {"cell_type": "code", "source": ["smiles_string = \"ClC=C=CCO\" #@param {type: 'string'}\n", "def smi2viewer(smi='CC=O'):\n", "    try:\n", "        conf = smi2conf(smi)\n", "        return MolTo3DView(conf).show()\n", "    except:\n", "        return None\n", "smi2viewer(smi=smiles_string)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 317}, "id": "NfMNBJ24rJAW", "outputId": "b2becc32-9f44-46af-c6f0-6e22f0f0a1dd", "cellView": "form"}, "execution_count": 15, "outputs": [{"output_type": "display_data", "data": {"application/3dmoljs_load.v0": "<div id=\"3dmolviewer_17557215405068092\"  style=\"position: relative; width: 300px; height: 300px;\">\n        <p id=\"3dmolwarning_17557215405068092\" style=\"background-color:#ffcccc;color:black\">3Dmol.js failed to load for some reason.  Please check your browser console for error messages.<br></p>\n        </div>\n<script>\n\nvar loadScriptAsync = function(uri){\n  return new Promise((resolve, reject) => {\n    //this is to ignore the existence of requirejs amd\n    var savedexports, savedmodule;\n    if (typeof exports !== 'undefined') savedexports = exports;\n    else exports = {}\n    if (typeof module !== 'undefined') savedmodule = module;\n    else module = {}\n\n    var tag = document.createElement('script');\n    tag.src = uri;\n    tag.async = true;\n    tag.onload = () => {\n        exports = savedexports;\n        module = savedmodule;\n        resolve();\n    };\n  var firstScriptTag = document.getElementsByTagName('script')[0];\n  firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);\n});\n};\n\nif(typeof $3Dmolpromise === 'undefined') {\n$3Dmolpromise = null;\n  $3Dmolpromise = loadScriptAsync('https://cdn.jsdelivr.net/npm/3dmol@2.5.2/build/3Dmol-min.js');\n}\n\nvar viewer_17557215405068092 = null;\nvar warn = document.getElementById(\"3dmolwarning_17557215405068092\");\nif(warn) {\n    warn.parentNode.removeChild(warn);\n}\n$3Dmolpromise.then(function() {\nviewer_17557215405068092 = $3Dmol.createViewer(document.getElementById(\"3dmolviewer_17557215405068092\"),{backgroundColor:\"white\"});\nviewer_17557215405068092.zoomTo();\n\tviewer_17557215405068092.addModel(\"\\n     RDKit          3D\\n\\n 11 10  0  0  0  0  0  0  0  0999 V2000\\n    2.7492    0.9938    0.8988 Cl  0  0  0  0  0  0  0  0  0  0  0  0\\n    2.1164   -0.5362    0.4939 C   0  0  0  0  0  0  0  0  0  0  0  0\\n    0.8683   -0.6196    0.1643 C   0  0  0  0  0  0  0  0  0  0  0  0\\n   -0.3892   -0.6922   -0.1651 C   0  0  0  0  0  0  0  0  0  0  0  0\\n   -1.1972    0.4377   -0.7066 C   0  0  0  0  0  0  0  0  0  0  0  0\\n   -2.1165    0.8539    0.2906 O   0  0  0  0  0  0  0  0  0  0  0  0\\n    2.8177   -1.3575    0.5417 H   0  0  0  0  0  0  0  0  0  0  0  0\\n   -0.9209   -1.6234    0.0081 H   0  0  0  0  0  0  0  0  0  0  0  0\\n   -1.7636    0.1099   -1.5831 H   0  0  0  0  0  0  0  0  0  0  0  0\\n   -0.5781    1.2926   -0.9971 H   0  0  0  0  0  0  0  0  0  0  0  0\\n   -1.5862    1.1410    1.0544 H   0  0  0  0  0  0  0  0  0  0  0  0\\n  1  2  1  0\\n  2  3  2  0\\n  3  4  2  0\\n  4  5  1  0\\n  5  6  1  0\\n  2  7  1  0\\n  4  8  1  0\\n  5  9  1  0\\n  5 10  1  0\\n  6 11  1  0\\nM  END\\n\",\"mol\");\n\tviewer_17557215405068092.setStyle({\"stick\": {}});\n\tviewer_17557215405068092.zoomTo();\nviewer_17557215405068092.render();\n});\n</script>", "text/html": ["<div id=\"3dmolviewer_17557215405068092\"  style=\"position: relative; width: 300px; height: 300px;\">\n", "        <p id=\"3dmolwarning_17557215405068092\" style=\"background-color:#ffcccc;color:black\">3Dmol.js failed to load for some reason.  Please check your browser console for error messages.<br></p>\n", "        </div>\n", "<script>\n", "\n", "var loadScriptAsync = function(uri){\n", "  return new Promise((resolve, reject) => {\n", "    //this is to ignore the existence of requirejs amd\n", "    var savedexports, savedmodule;\n", "    if (typeof exports !== 'undefined') savedexports = exports;\n", "    else exports = {}\n", "    if (typeof module !== 'undefined') savedmodule = module;\n", "    else module = {}\n", "\n", "    var tag = document.createElement('script');\n", "    tag.src = uri;\n", "    tag.async = true;\n", "    tag.onload = () => {\n", "        exports = savedexports;\n", "        module = savedmodule;\n", "        resolve();\n", "    };\n", "  var firstScriptTag = document.getElementsByTagName('script')[0];\n", "  firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);\n", "});\n", "};\n", "\n", "if(typeof $3Dmolpromise === 'undefined') {\n", "$3Dmolpromise = null;\n", "  $3Dmolpromise = loadScriptAsync('https://cdn.jsdelivr.net/npm/3dmol@2.5.2/build/3Dmol-min.js');\n", "}\n", "\n", "var viewer_17557215405068092 = null;\n", "var warn = document.getElementById(\"3dmolwarning_17557215405068092\");\n", "if(warn) {\n", "    warn.parentNode.removeChild(warn);\n", "}\n", "$3Dmolpromise.then(function() {\n", "viewer_17557215405068092 = $3Dmol.createViewer(document.getElementById(\"3dmolviewer_17557215405068092\"),{backgroundColor:\"white\"});\n", "viewer_17557215405068092.zoomTo();\n", "\tviewer_17557215405068092.addModel(\"\\n     RDKit          3D\\n\\n 11 10  0  0  0  0  0  0  0  0999 V2000\\n    2.7492    0.9938    0.8988 Cl  0  0  0  0  0  0  0  0  0  0  0  0\\n    2.1164   -0.5362    0.4939 C   0  0  0  0  0  0  0  0  0  0  0  0\\n    0.8683   -0.6196    0.1643 C   0  0  0  0  0  0  0  0  0  0  0  0\\n   -0.3892   -0.6922   -0.1651 C   0  0  0  0  0  0  0  0  0  0  0  0\\n   -1.1972    0.4377   -0.7066 C   0  0  0  0  0  0  0  0  0  0  0  0\\n   -2.1165    0.8539    0.2906 O   0  0  0  0  0  0  0  0  0  0  0  0\\n    2.8177   -1.3575    0.5417 H   0  0  0  0  0  0  0  0  0  0  0  0\\n   -0.9209   -1.6234    0.0081 H   0  0  0  0  0  0  0  0  0  0  0  0\\n   -1.7636    0.1099   -1.5831 H   0  0  0  0  0  0  0  0  0  0  0  0\\n   -0.5781    1.2926   -0.9971 H   0  0  0  0  0  0  0  0  0  0  0  0\\n   -1.5862    1.1410    1.0544 H   0  0  0  0  0  0  0  0  0  0  0  0\\n  1  2  1  0\\n  2  3  2  0\\n  3  4  2  0\\n  4  5  1  0\\n  5  6  1  0\\n  2  7  1  0\\n  4  8  1  0\\n  5  9  1  0\\n  5 10  1  0\\n  6 11  1  0\\nM  END\\n\",\"mol\");\n", "\tviewer_17557215405068092.setStyle({\"stick\": {}});\n", "\tviewer_17557215405068092.zoomTo();\n", "viewer_17557215405068092.render();\n", "});\n", "</script>"]}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["Run 3DRISM"], "metadata": {"id": "l3KKjL8Bun7b"}}, {"cell_type": "code", "source": ["print(\"your free energy of solvation is \",generate_topology_and_get_energy([smiles_string]),'kcal/mol')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cxCSsPWlunsb", "outputId": "0282b325-2e90-4bb0-a7cc-b043eecc1806"}, "execution_count": 16, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.12/site-packages/MDAnalysis/coordinates/GRO.py:444: UserWarning: Supplied AtomGroup was missing the following attributes: resnames. These will be written with default values. Alternatively these can be supplied as keyword arguments.\n", "  warnings.warn(\n"]}, {"output_type": "stream", "name": "stdout", "text": ["converted out.top to out.solute\n", "generated idc-enabled solute file to: idc_out.solute\n", "Calculation finished in 256 steps \n", "err_tol: 1e-12 actual: 9.17948e-13 \n", "your free energy of solvation is  [-6.233348018396457] kcal/mol\n"]}]}]}