#@title ##Download and Install Episol
#@markdown ($\approx 2$min) Stable as of 07/01/25 eprism v1.2.6
%%capture
import subprocess
import pandas as pd
import matplotlib.pyplot as plt
#%cd ../home/
%cd $HOME/
%mkdir episol
%cd episol
!wget https://github.com/EPISOLrelease/EPISOL/raw/refs/heads/main/src/fftw/fftw-3.3.8.tar.gz
!echo "+++++++++++++++++++"
!echo "downloaded fftw files"
!echo "+++++++++++++++++++"
!tar -xzf fftw-3.3.8.tar.gz
%cd fftw-3.3.8/
#!./configure --prefix=/home/<USER>
!./configure --prefix=$HOME/episol/fftw-3.3.8
!make
!make install
%cd ../
!wget https://github.com/EPISOLrelease/EPISOL/raw/refs/heads/main/src/kernel/release.tar.gz
!echo "+++++++++++++++++++"
!echo "downloaded Episol files"
!echo "+++++++++++++++++++"
!tar -xzf release.tar.gz
%cd release/
#!./configure --with-fftw=/home/<USER>
!./configure --with-fftw=$HOME/episol/fftw-3.3.8
!make
!make install
#%cd /content
########################### WRAPEPR
import subprocess
import os
import threading
import pandas as pd
import matplotlib.pyplot as plt
!pip install episol


%%capture

#@title Install some python packages for topology generation
#@markdown This will prompt a restart in our colab session, this is necessary, just keep moving

#@markdown (if you are using the notebook offline this wont be necessary, as presumably you'll have your own forcefield to generate topologies)

!pip install -q condacolab
import condacolab
condacolab.install()
#!conda update conda
#!conda install --yes -c conda-forge  python=3.11 numpy=1.26.4 openmm pdbfixer parmed mdanalysis py3dmol rdkit openff-toolkit
#!conda install -y -c conda-forge numpy=1.26.4 openmm=8.3.1 python={PYTHON_VERSION} pdbfixer=1.11 parmed=4.3.0 mdanalysis=2.9.0 py3dmol=2.5.2 rdkit=2025.03.5 openff-toolkit=0.17.0 libgcc
!conda install -y -c conda-forge python=3.12 numpy=1.26.4 openmm=8.3.1 pdbfixer=1.11 parmed=4.3.0 mdanalysis=2.9.0 py3dmol=2.5.2 rdkit=2025.03.5 openff-toolkit=0.17.0 torchvision
#openmm pdbfixer parmed mdanalysis py3dmol rdkitconda install libgcc

#@title import our download packages
%%capture
!python -m ensurepip --upgrade # since we are using python 3.12 some pkg utils are now obsolete
# after conda-initiate restart colab resets pip
import matplotlib.pyplot as plt
import openmm as mm
from   openmm import app
from   openmm.unit import *
import py3Dmol as pymol
import MDAnalysis as md
import parmed as chem
from openff.toolkit.topology import Molecule, Topology
import numpy as np
from MDAnalysis.transformations import center_in_box
from episol import epipy

methane = epipy('./episol/release/solute/methane.gro','./episol/release/solute/methane.top')

print('Our (default) solvent is in',methane.solvent_path)
print('Our solute is in',methane.solute_path)
print('Our output file will be named:',methane.log)
print('episol executable is in:',methane.get_eprism_path)

methane.get_version()

methane.get_help('coulomb')

methane.report('methane_tutorial',args=('all'))

methane.rism(resolution=0.5)

methane.grid

methane.rism(resolution=1)
methane.grid

methane.rism(step=500)
methane.err_tol = 1e-06

methane.test()

methane.kernel()

methane.dump(list_values=True)

g_r = methane.select_grid('guv')

g_r.shape


z_slice = 9 # @param {type:"slider", min:1, max:30, step:1}
fig,ax = plt.subplots()
#z_slice = 10
p = ax.pcolormesh(g_r[z_slice])
ax.set_ylabel(f'y grid / {methane.resolution}$\\mathring{{A}}$')
ax.set_xlabel(f'x grid / {methane.resolution}$\\mathring{{A}}$')

fig.colorbar(p,ax=ax, label="$g(\\vec{r})/\\mathring{{A}}$")
fig.suptitle(f'$g(\\vec{{r}})$ at slice {z_slice*methane.resolution} $\\mathring{{A}}$')

methane.report('methane_res_05A') # now we write out to a new set of files
methane.rism(resolution=0.5)
methane.kernel()
high_res_g_r = methane.select_grid('guv')

z_slice = 9 # @param {type:"slider", min:1, max:30, step:1}
fig,ax = plt.subplots()
#z_slice = 10
p = ax.pcolormesh(high_res_g_r[z_slice])
ax.set_ylabel(f'y grid / {methane.resolution}$\\mathring{{A}}$')
ax.set_xlabel(f'x grid / {methane.resolution}$\\mathring{{A}}$')

fig.colorbar(p,ax=ax, label="$g(\\vec{r})/\\mathring{{A}}$")
fig.suptitle(f'$g(\\vec{{r}})$ at slice {z_slice*methane.resolution} $\\mathring{{A}}$')

methane.reader(file_in='guv_methane_tutorial.txt',file_out='guv_methane',dx=True)

#@title We will download the structure file from PubChem
#@markdown NOTE: the following code will work for essentailly ANY molecule

#@markdown you download as an .sdf file so feel free to get creative!
!wget -O nitrous.sdf https://files.rcsb.org/ligands/download/NO2_ideal.sdf

#@title Generate topology
%%capture
from openff.toolkit.topology import Molecule
from openff.toolkit.utils import get_data_file_path
from openff.toolkit.typing.engines.smirnoff import ForceField
from openff.interchange import Interchange
#sdf_file_path = get_data_file_path("nitrous.sdf")
ps = chem.load_file('nitrous.sdf')
molecule: Molecule = Molecule.from_file("nitrous.sdf")
topology: Topology = molecule.to_topology()
sage = ForceField("openff-2.0.0.offxml")
interchange = Interchange.from_smirnoff(force_field=sage, topology=topology)
interchange.positions = molecule.conformers[0]
openmm_system = interchange.to_openmm()
interchange.to_top("out.top")

## Here we set the box dimensions and center our molecule for viewing purposes
nn = md.Universe(chem.load_file('nitrous.sdf',structure=True))
nn.dimensions = np.array([30,30,30,90,90,90])
nn.dimensions
print(nn.atoms.positions)
tt = center_in_box(nn.atoms,center='geometry')
nn.trajectory.add_transformations(tt)
print(nn.atoms.positions)
nn.atoms.write('nitrous_.gro')

nitrous_ = epipy('nitrous_.gro','out.top')
nitrous_.err_tol = 1e-08
nitrous_.rism(step=1000,resolution=0.25) # we will use maximum resolution!
nitrous_.kernel()

n_g_r = nitrous_.select_grid('guv')
n_g_r.shape

z_slice = 60 # @param {type:"slider", min:1, max:120, step:1}
fig,ax = plt.subplots()
#z_slice = 10
p = ax.pcolormesh(n_g_r[:,:,z_slice])
ax.set_ylabel(f'y grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax.set_xlabel(f'x grid / {nitrous_.resolution}$\\mathring{{A}}$')
fig.colorbar(p,ax=ax, label="$g(\\vec{r})/\\mathring{{A}}$")
fig.suptitle(f'$g(\\vec{{r}})$ at x-slice {z_slice*nitrous_.resolution} $\\mathring{{A}}$')

idc_nitrous = epipy('nitrous_.gro','out.top',gen_idc=True)

idc_nitrous.report('idc_nitrous')
idc_nitrous.err_tol=1e-08
idc_nitrous.rism(step=1000,resolution=0.25)
idc_nitrous.kernel()

g_r_idc = idc_nitrous.select_grid('guv')

z_slice = 60 # @param {type:"slider", min:1, max:120, step:1}
fig,ax = plt.subplots()
#z_slice = 10
p = ax.pcolormesh(g_r_idc[:,:,z_slice])
ax.set_ylabel(f'y grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax.set_xlabel(f'x grid / {nitrous_.resolution}$\\mathring{{A}}$')
fig.colorbar(p,ax=ax, label="$g(\\vec{r})/\\mathring{{A}}$")
fig.suptitle(f'$g(\\vec{{r}})$ at x-slice {z_slice*nitrous_.resolution} $\\mathring{{A}}$')
ax.set_ylim(20,100)
ax.set_xlim(20,100)

z_slice = 60 # @param {type:"slider", min:1, max:120, step:1}
#g_r_idc = nitrous_.reader(file_in='guv_idc_nitrous.txt')
#g_r = nitrous_.reader(file_in='guv_nitrous.txt')

fig,(ax1,ax2,ax3) = plt.subplots(1,3,figsize=(12,4))

p1 = ax1.pcolormesh(n_g_r[:,:,z_slice])
ax1.set_ylabel(f'y grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax1.set_xlabel(f'x grid / {nitrous_.resolution}$\\mathring{{A}}$')
#ax1.colorbar(p,ax=ax, label="$g(\\vec{r})/\\mathring{{A}}$")
fig.colorbar(p1,ax=ax1, label="$g(\\vec{r})$ / $\\mathring{{A}}$")
#ax1.set_yticks([])
ax1.set_title("$g(\\vec{r})$")
ax1.set_ylim(20,100)
ax1.set_xlim(20,100)

p2 = ax2.pcolormesh(g_r_idc[:,:,z_slice])
#ax2.set_ylabel(f'y grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax2.set_xlabel(f'x grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax2.set_title("$g(\\vec{r})_{IDC}$")
fig.colorbar(p2,ax=ax2, label="$g(\\vec{r})_{IDC}$ / $\\mathring{{A}}$")
ax2.set_yticks([])
ax2.set_ylim(20,100)
ax2.set_xlim(20,100)

p3 = ax3.pcolormesh(np.abs(g_r_idc[:,:,z_slice]-n_g_r[:,:,z_slice]))
#ax3.set_ylabel(f'y grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax3.set_xlabel(f'x grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax3.set_title("$\\Delta g(\\vec{r})$")
fig.colorbar(p3,ax=ax3, label="$|g(\\vec{r})-g(\\vec{r})_{IDC}|$ / $\\mathring{{A}}$")
ax3.set_yticks([])
ax3.set_ylim(20,100)
ax3.set_xlim(20,100)

fig.suptitle(f'$g(\\vec{{r}})$ at x-slice {z_slice*nitrous_.resolution} $\\mathring{{A}}$, IDC comparison')
fig.tight_layout()

T_n_g_r = nitrous_.select_grid('convolve guv')
T2_g_r_idc = idc_nitrous.select_grid('convolve guv')

z_slice = 60 # @param {type:"slider", min:1, max:120, step:1}
#g_r_idc = nitrous_.reader(file_in='guv_idc_nitrous.txt')
#g_r = nitrous_.reader(file_in='guv_nitrous.txt')


fig,(ax1,ax2,ax3) = plt.subplots(1,3,figsize=(12,4))

p1 = ax1.pcolormesh(T_n_g_r[:,:,z_slice])
ax1.set_ylabel(f'y grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax1.set_xlabel(f'x grid / {nitrous_.resolution}$\\mathring{{A}}$')
#ax1.colorbar(p,ax=ax, label="$g(\\vec{r})/\\mathring{{A}}$")
fig.colorbar(p1,ax=ax1, label="$\\rho/\\rho^b $")
#ax1.set_yticks([])
ax1.set_title("$\\rho/\\rho^b$")
ax1.set_ylim(20,100)
ax1.set_xlim(20,100)

p2 = ax2.pcolormesh(T2_g_r_idc[:,:,z_slice])
#ax2.set_ylabel(f'y grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax2.set_xlabel(f'x grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax2.set_title("$\\rho/\\rho^b_{IDC}$")
fig.colorbar(p2,ax=ax2, label="$\\rho/\\rho^b_{IDC}$")
ax2.set_yticks([])
ax2.set_ylim(20,100)
ax2.set_xlim(20,100)

p3 = ax3.pcolormesh(np.abs(T_n_g_r[:,:,z_slice]-T2_g_r_idc[:,:,z_slice]))
#ax3.set_ylabel(f'y grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax3.set_xlabel(f'x grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax3.set_title("$\\Delta \\rho/\\rho^b$")
fig.colorbar(p3,ax=ax3, label="$|\\rho-\\rho_{IDC}|$ / $\\mathring{{A}}$")
ax3.set_yticks([])
ax3.set_ylim(20,100)
ax3.set_xlim(20,100)

fig.suptitle(f'$\\rho/\\rho^b$ slice at {z_slice*nitrous_.resolution} $\\mathring{{A}}$, IDC comparison')
fig.tight_layout()

lap_g_r_idc = idc_nitrous.select_grid('LoG guv')

g_r2 = idc_nitrous.select_grid('convolve coul')

z_slice = 60 # @param {type:"slider", min:1, max:120, step:1}
fig,(ax,ax1) = plt.subplots(1,2,figsize=(10,5))
#lap_g_r_idc = idc_nitrous.select_grid('grad guv')

cmap = plt.colormaps['turbo']
p = ax.pcolormesh(lap_g_r_idc[:,:,z_slice],cmap=cmap)
p1 = ax1.pcolormesh(g_r2[:,:,z_slice],cmap=cmap)

fig.colorbar(p,ax=ax, label="$\\nabla \\rho_{IDC}$")

fig.colorbar(p1,ax=ax1, label="$\\nabla \\rho(U(r))_{IDC}$")

ax.set_ylabel(f'y grid / {idc_nitrous.resolution}$\\mathring{{A}}$')
ax.set_xlabel(f'x grid / {idc_nitrous.resolution}$\\mathring{{A}}$')
ax1.set_xlabel(f'x grid / {idc_nitrous.resolution}$\\mathring{{A}}$')
ax1.set_yticks([])
fig.suptitle(f'slice at {z_slice*idc_nitrous.resolution} $\\mathring{{A}}$')
fig.tight_layout()

z_slice = 60 # @param {type:"slider", min:1, max:120, step:1}
#g_r_idc = nitrous_.reader(file_in='guv_idc_nitrous.txt')
fig,ax = plt.subplots()
#z_slice = 10
nit_coords = []
with open('nitrous_.gro','r') as f:
  for line in f:
    tmp = line.split()
    if tmp[0] == '1UNL':
      nit_coords.append([float(tmp[3]),
                         float(tmp[4]),
                         float(tmp[5])])
cmap = plt.colormaps['turbo']
x = np.arange(0,len(g_r_idc))
y = np.arange(0,len(g_r_idc))
z = np.arange(0,len(g_r_idc))
nit_coords = np.array(nit_coords)
r = 1.5/nitrous_.resolution
for val in nit_coords:
  mask = (x[:,np.newaxis,np.newaxis]-val[0]/nitrous_.resolution*10)**2 + (y[np.newaxis,:,np.newaxis]-val[1]/nitrous_.resolution*10)**2 +(y[np.newaxis,np.newaxis,:]-val[2]/nitrous_.resolution*10)**2 < r**2
  g_r_idc[mask] = np.nan
p = ax.pcolormesh(g_r_idc[:,:,z_slice],cmap=cmap)
ax.scatter(nit_coords[:,1]/nitrous_.resolution*10,nit_coords[:,0]/nitrous_.resolution*10,s=220, facecolors='none', edgecolors='black')
#ax.plot(nit_coords[:,0]/nitrous_.resolution*10,nit_coords[:,1]/nitrous_.resolution*10,c='black')
names = ['N','O','O']
for name,x,y in zip(names,nit_coords[:,1]/nitrous_.resolution*10,nit_coords[:,0]/nitrous_.resolution*10):
  ax.text(x-1,y-1,f'{name}')
#fig.colorbar(p,ax=ax)
ax.set_ylim(20,100)
ax.set_xlim(20,100)
ax.set_ylabel(f'y grid / {nitrous_.resolution}$\\mathring{{A}}$')
ax.set_xlabel(f'x grid / {nitrous_.resolution}$\\mathring{{A}}$')
fig.suptitle(f'$g(\\vec{{r}})$ at x-slice {z_slice*nitrous_.resolution} $\\mathring{{A}}$')

fig.colorbar(p,ax=ax, label="$g(\\vec{r})_{IDC}$ / $\\mathring{{A}}$")