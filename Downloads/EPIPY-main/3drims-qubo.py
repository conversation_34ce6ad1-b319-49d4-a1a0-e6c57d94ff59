import numpy as np
import matplotlib.pyplot as plt
from pyqubo import Array, solve_qubo

# --- 1. 问题设置 (简化的二维示例) ---
# 为简单起见，我们在二维网格上进行演示
GRID_SIZE = 50
X, Y = np.meshgrid(np.linspace(0, 10, GRID_SIZE), np.linspace(0, 10, GRID_SIZE))
GRID_STEP = X[0, 1] - X[0, 0] # 网格步长，用于数值积分

# 定义一个函数来创建二维高斯分布
def gaussian(x0, y0, sigma):
    """在(x0, y0)处生成一个标准差为sigma的2D高斯函数"""
    return np.exp(-((X - x0)**2 + (Y - y0)**2) / (2 * sigma**2))

# --- 2. 创建目标密度 g(r) ---
# 这模拟了3D-RISM数据，即我们希望近似的“水汽”图。
# 我们创建了一个具有两个峰值的目标密度。
sigma_target = 0.8
g_r = gaussian(3, 3, sigma_target) + gaussian(7, 6, sigma_target)

# --- 3. 定义水分子的候选位置 ---
# 这些是我们可以放置水分子的潜在位置 (q_i)。
num_candidates_per_dim = 8
x_q = np.linspace(1, 9, num_candidates_per_dim)
y_q = np.linspace(1, 9, num_candidates_per_dim)
q_coords = np.array(np.meshgrid(x_q, y_q)).T.reshape(-1, 2)
M = len(q_coords) # 候选位置总数

# 我们用来搭建模型的“积木”高斯函数的标准差
sigma_model = 0.7
candidate_gaussians = [gaussian(qx, qy, sigma_model) for qx, qy in q_coords]

# --- 4. 计算QUBO系数 (Gamma_i 和 V_ij) ---
# 公式源于最小化L2范数: Integral[(g(r) - Sum(n_i * G_i(r)))^2] dr
# 展开后得到哈密顿量: H = Constant + Sum_i (Gamma_i * n_i) + Sum_{i!=j} (V_ij * n_i * n_j)
# 我们用网格上的求和来近似积分。

def integral(f):
    """通过求和来近似二维积分"""
    return np.sum(f) * (GRID_STEP**2)

# 计算 V_ij: 两个不同候选高斯函数 i 和 j 之间的重叠
V = np.zeros((M, M))
for i in range(M):
    for j in range(i + 1, M):
        # 根据论文，V_ij 项是 G_i * G_j 的积分
        V[i, j] = integral(candidate_gaussians[i] * candidate_gaussians[j])

# 计算 Gamma_i: 线性项系数
# 根据论文推导 (Eq. 4, 6), n_i 的线性系数来自两部分:
# 1. 交叉项: -2 * Integral(g(r) * G_i(r))
# 2. 平方项中 i=j 的情况: Integral(G_i(r)^2) (因为 n_i^2 = n_i)
# 因此, Gamma_i = -2 * Integral(g * G_i) + Integral(G_i^2)
Gamma = np.zeros(M)
for i in range(M):
    integral_g_gi = integral(g_r * candidate_gaussians[i])
    integral_gi_sq = integral(candidate_gaussians[i]**2)
    Gamma[i] = -2 * integral_g_gi + integral_gi_sq

# --- 5. 使用 pyqubo 构建 QUBO 模型 ---
# 创建二元变量数组，代表每个候选位置是否放置水分子
n = Array.create('n', shape=M, vartype='BINARY')

# 定义需要最小化的哈密顿量 H (成本函数)
H = 0.0
# 添加线性项 (放置单个分子的奖励/成本)
H += sum(Gamma[i] * n[i] for i in range(M))
# 添加二次项 (同时放置两个分子的惩罚)
# 乘以2是因为公式展开后是 n_i*n_j + n_j*n_i
H += sum(2 * V[i, j] * n[i] * n[j] for i in range(M) for j in range(i + 1, M))

model = H.compile()
qubo, offset = model.to_qubo()

# --- 6. 求解 QUBO ---
# 我们使用 pyqubo 后端的经典求解器 (模拟退火)
solution = solve_qubo(qubo)

# 解码解决方案，得到每个位置的 0 或 1
decoded_solution = model.decode_sample(solution, vartype='BINARY')
placements = np.array([decoded_solution.array('n', i) for i in range(M)])

# --- 7. 结果可视化 ---
# 找出所有 n_i = 1 的位置坐标
final_placement_coords = q_coords[placements == 1]

# 根据QUBO解构建最终的密度图
final_density = np.zeros_like(X)
if final_placement_coords.size > 0:
    for i in range(M):
       if placements[i] == 1:
           final_density += candidate_gaussians[i]

# 绘图
fig, axes = plt.subplots(1, 3, figsize=(20, 6))

# 图1: 目标密度 g(r)
ax1 = axes[0]
contour1 = ax1.contourf(X, Y, g_r, levels=20, cmap='viridis')
ax1.set_title('Target "Water Vapor" Density g(r)')
ax1.set_xlabel('X')
ax1.set_ylabel('Y')
fig.colorbar(contour1, ax=ax1)

# 图2: 候选位置
ax2 = axes[1]
ax2.contourf(X, Y, g_r, levels=20, cmap='viridis', alpha=0.5)
ax2.scatter(q_coords[:, 0], q_coords[:, 1], c='red', marker='x', s=50, label='Candidate Positions (q_i)')
ax2.set_title('Candidate Positions on Target Density')
ax2.set_xlabel('X')
ax2.set_ylabel('Y')
ax2.legend()

# 图3: QUBO求解后的最终布局
ax3 = axes[2]
if np.any(final_density):
    contour3 = ax3.contourf(X, Y, final_density, levels=20, cmap='viridis')
    fig.colorbar(contour3, ax=ax3)
if final_placement_coords.size > 0:
    ax3.scatter(final_placement_coords[:, 0], final_placement_coords[:, 1],
                edgecolor='red', facecolor='none', s=150, linewidth=2, label=f'Final Placements ({len(final_placement_coords)} molecules)')
ax3.set_title('QUBO Solution: Predicted Water Positions')
ax3.set_xlabel('X')
ax3.set_ylabel('Y')
ax3.legend(loc='upper right')
ax3.set_aspect('equal', adjustable='box')

plt.tight_layout()
plt.savefig("qubo_water_placement_simulation.png")
plt.show()

print(f"找到 {len(final_placement_coords)} 个水分子位置。")
print("绘图已保存为 qubo_water_placement_simulation.png")